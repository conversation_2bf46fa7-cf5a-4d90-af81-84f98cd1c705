(() => {
  var e = {
      647: (e, t, n) => {
        function r(e) {
          this.options = e || { locator: {} };
        }
        function o() {
          this.cdata = !1;
        }
        function i(e, t) {
          ((t.lineNumber = e.lineNumber), (t.columnNumber = e.columnNumber));
        }
        function a(e) {
          if (e)
            return (
              "\n@" +
              (e.systemId || "") +
              "#[line:" +
              e.lineNumber +
              ",col:" +
              e.columnNumber +
              "]"
            );
        }
        function s(e, t, n) {
          return "string" == typeof e
            ? e.substr(t, n)
            : e.length >= t + n || t
              ? new java.lang.String(e, t, n) + ""
              : e;
        }
        function d(e, t) {
          e.currentElement
            ? e.currentElement.appendChild(t)
            : e.doc.appendChild(t);
        }
        ((r.prototype.parseFromString = function (e, t) {
          var n = this.options,
            r = new l(),
            i = n.domBuilder || new o(),
            s = n.errorHandler,
            d = n.locator,
            c = n.xmlns || {},
            f = /\/x?html?$/.test(t),
            p = f
              ? u.entityMap
              : { lt: "<", gt: ">", amp: "&", quot: '"', apos: "'" };
          return (
            d && i.setDocumentLocator(d),
            (r.errorHandler = (function (e, t, n) {
              if (!e) {
                if (t instanceof o) return t;
                e = t;
              }
              var r = {},
                i = e instanceof Function;
              function s(t) {
                var o = e[t];
                (!o &&
                  i &&
                  (o =
                    2 == e.length
                      ? function (n) {
                          e(t, n);
                        }
                      : e),
                  (r[t] =
                    (o &&
                      function (e) {
                        o("[xmldom " + t + "]\t" + e + a(n));
                      }) ||
                    function () {}));
              }
              return (
                (n = n || {}),
                s("warning"),
                s("error"),
                s("fatalError"),
                r
              );
            })(s, i, d)),
            (r.domBuilder = n.domBuilder || i),
            f && (c[""] = "http://www.w3.org/1999/xhtml"),
            (c.xml = c.xml || "http://www.w3.org/XML/1998/namespace"),
            e && "string" == typeof e
              ? r.parse(e, c, p)
              : r.errorHandler.error("invalid doc source"),
            i.doc
          );
        }),
          (o.prototype = {
            startDocument: function () {
              ((this.doc = new p().createDocument(null, null, null)),
                this.locator && (this.doc.documentURI = this.locator.systemId));
            },
            startElement: function (e, t, n, r) {
              var o = this.doc,
                a = o.createElementNS(e, n || t),
                s = r.length;
              (d(this, a),
                (this.currentElement = a),
                this.locator && i(this.locator, a));
              for (var u = 0; u < s; u++) {
                e = r.getURI(u);
                var c = r.getValue(u),
                  l = ((n = r.getQName(u)), o.createAttributeNS(e, n));
                (this.locator && i(r.getLocator(u), l),
                  (l.value = l.nodeValue = c),
                  a.setAttributeNode(l));
              }
            },
            endElement: function (e, t, n) {
              var r = this.currentElement;
              r.tagName;
              this.currentElement = r.parentNode;
            },
            startPrefixMapping: function (e, t) {},
            endPrefixMapping: function (e) {},
            processingInstruction: function (e, t) {
              var n = this.doc.createProcessingInstruction(e, t);
              (this.locator && i(this.locator, n), d(this, n));
            },
            ignorableWhitespace: function (e, t, n) {},
            characters: function (e, t, n) {
              if ((e = s.apply(this, arguments))) {
                if (this.cdata) var r = this.doc.createCDATASection(e);
                else r = this.doc.createTextNode(e);
                (this.currentElement
                  ? this.currentElement.appendChild(r)
                  : /^\s*$/.test(e) && this.doc.appendChild(r),
                  this.locator && i(this.locator, r));
              }
            },
            skippedEntity: function (e) {},
            endDocument: function () {
              this.doc.normalize();
            },
            setDocumentLocator: function (e) {
              (this.locator = e) && (e.lineNumber = 0);
            },
            comment: function (e, t, n) {
              e = s.apply(this, arguments);
              var r = this.doc.createComment(e);
              (this.locator && i(this.locator, r), d(this, r));
            },
            startCDATA: function () {
              this.cdata = !0;
            },
            endCDATA: function () {
              this.cdata = !1;
            },
            startDTD: function (e, t, n) {
              var r = this.doc.implementation;
              if (r && r.createDocumentType) {
                var o = r.createDocumentType(e, t, n);
                (this.locator && i(this.locator, o), d(this, o));
              }
            },
            warning: function (e) {
              console.warn("[xmldom warning]\t" + e, a(this.locator));
            },
            error: function (e) {
              console.error("[xmldom error]\t" + e, a(this.locator));
            },
            fatalError: function (e) {
              throw new f(e, this.locator);
            },
          }),
          "endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(
            /\w+/g,
            function (e) {
              o.prototype[e] = function () {
                return null;
              };
            },
          ));
        var u = n(791),
          c = n(275),
          l = c.XMLReader,
          f = c.ParseError,
          p = n(34).DOMImplementation;
        (n(34), (t.a = r));
      },
      34: (e, t) => {
        function n(e, t) {
          for (var n in e) t[n] = e[n];
        }
        function r(e, t) {
          var r = e.prototype;
          if (!(r instanceof t)) {
            function o() {}
            ((o.prototype = t.prototype),
              n(r, (o = new o())),
              (e.prototype = r = o));
          }
          r.constructor != e &&
            ("function" != typeof e && console.error("unknow Class:" + e),
            (r.constructor = e));
        }
        var o = "http://www.w3.org/1999/xhtml",
          i = {},
          a = (i.ELEMENT_NODE = 1),
          s = (i.ATTRIBUTE_NODE = 2),
          d = (i.TEXT_NODE = 3),
          u = (i.CDATA_SECTION_NODE = 4),
          c = (i.ENTITY_REFERENCE_NODE = 5),
          l = (i.ENTITY_NODE = 6),
          f = (i.PROCESSING_INSTRUCTION_NODE = 7),
          p = (i.COMMENT_NODE = 8),
          m = (i.DOCUMENT_NODE = 9),
          h = (i.DOCUMENT_TYPE_NODE = 10),
          g = (i.DOCUMENT_FRAGMENT_NODE = 11),
          w = (i.NOTATION_NODE = 12),
          v = {},
          b = {},
          y =
            ((v.INDEX_SIZE_ERR = ((b[1] = "Index size error"), 1)),
            (v.DOMSTRING_SIZE_ERR = ((b[2] = "DOMString size error"), 2)),
            (v.HIERARCHY_REQUEST_ERR =
              ((b[3] = "Hierarchy request error"), 3))),
          N =
            ((v.WRONG_DOCUMENT_ERR = ((b[4] = "Wrong document"), 4)),
            (v.INVALID_CHARACTER_ERR = ((b[5] = "Invalid character"), 5)),
            (v.NO_DATA_ALLOWED_ERR = ((b[6] = "No data allowed"), 6)),
            (v.NO_MODIFICATION_ALLOWED_ERR =
              ((b[7] = "No modification allowed"), 7)),
            (v.NOT_FOUND_ERR = ((b[8] = "Not found"), 8))),
          x =
            ((v.NOT_SUPPORTED_ERR = ((b[9] = "Not supported"), 9)),
            (v.INUSE_ATTRIBUTE_ERR = ((b[10] = "Attribute in use"), 10)));
        ((v.INVALID_STATE_ERR = ((b[11] = "Invalid state"), 11)),
          (v.SYNTAX_ERR = ((b[12] = "Syntax error"), 12)),
          (v.INVALID_MODIFICATION_ERR = ((b[13] = "Invalid modification"), 13)),
          (v.NAMESPACE_ERR = ((b[14] = "Invalid namespace"), 14)),
          (v.INVALID_ACCESS_ERR = ((b[15] = "Invalid access"), 15)));
        function E(e, t) {
          if (t instanceof Error) var n = t;
          else
            ((n = this),
              Error.call(this, b[e]),
              (this.message = b[e]),
              Error.captureStackTrace && Error.captureStackTrace(this, E));
          return (
            (n.code = e),
            t && (this.message = this.message + ": " + t),
            n
          );
        }
        function O() {}
        function S(e, t) {
          ((this._node = e), (this._refresh = t), D(this));
        }
        function D(e) {
          var t = e._node._inc || e._node.ownerDocument._inc;
          if (e._inc != t) {
            var r = e._refresh(e._node);
            (oe(e, "length", r.length), n(r, e), (e._inc = t));
          }
        }
        function I() {}
        function T(e, t) {
          for (var n = e.length; n--; ) if (e[n] === t) return n;
        }
        function A(e, t, n, r) {
          if ((r ? (t[T(t, r)] = n) : (t[t.length++] = n), e)) {
            n.ownerElement = e;
            var o = e.ownerDocument;
            o &&
              (r && k(o, e, r),
              (function (e, t, n) {
                e && e._inc++;
                var r = n.namespaceURI;
                "http://www.w3.org/2000/xmlns/" == r &&
                  (t._nsMap[n.prefix ? n.localName : ""] = n.value);
              })(o, e, n));
          }
        }
        function C(e, t, n) {
          var r = T(t, n);
          if (!(r >= 0)) throw E(N, new Error(e.tagName + "@" + n));
          for (var o = t.length - 1; r < o; ) t[r] = t[++r];
          if (((t.length = o), e)) {
            var i = e.ownerDocument;
            i && (k(i, e, n), (n.ownerElement = null));
          }
        }
        function M(e) {
          if (((this._features = {}), e))
            for (var t in e) this._features = e[t];
        }
        function _() {}
        function U(e) {
          return (
            ("<" == e ? "&lt;" : ">" == e && "&gt;") ||
            ("&" == e && "&amp;") ||
            ('"' == e && "&quot;") ||
            "&#" + e.charCodeAt() + ";"
          );
        }
        function $(e, t) {
          if (t(e)) return !0;
          if ((e = e.firstChild))
            do {
              if ($(e, t)) return !0;
            } while ((e = e.nextSibling));
        }
        function R() {}
        function k(e, t, n, r) {
          (e && e._inc++,
            "http://www.w3.org/2000/xmlns/" == n.namespaceURI &&
              delete t._nsMap[n.prefix ? n.localName : ""]);
        }
        function B(e, t, n) {
          if (e && e._inc) {
            e._inc++;
            var r = t.childNodes;
            if (n) r[r.length++] = n;
            else {
              for (var o = t.firstChild, i = 0; o; )
                ((r[i++] = o), (o = o.nextSibling));
              r.length = i;
            }
          }
        }
        function P(e, t) {
          var n = t.previousSibling,
            r = t.nextSibling;
          return (
            n ? (n.nextSibling = r) : (e.firstChild = r),
            r ? (r.previousSibling = n) : (e.lastChild = n),
            B(e.ownerDocument, e),
            t
          );
        }
        function F(e, t, n) {
          var r = t.parentNode;
          if ((r && r.removeChild(t), t.nodeType === g)) {
            var o = t.firstChild;
            if (null == o) return t;
            var i = t.lastChild;
          } else o = i = t;
          var a = n ? n.previousSibling : e.lastChild;
          ((o.previousSibling = a),
            (i.nextSibling = n),
            a ? (a.nextSibling = o) : (e.firstChild = o),
            null == n ? (e.lastChild = i) : (n.previousSibling = i));
          do {
            o.parentNode = e;
          } while (o !== i && (o = o.nextSibling));
          return (
            B(e.ownerDocument || e, e),
            t.nodeType == g && (t.firstChild = t.lastChild = null),
            t
          );
        }
        function V() {
          this._nsMap = {};
        }
        function L() {}
        function W() {}
        function q() {}
        function z() {}
        function Q() {}
        function j() {}
        function H() {}
        function K() {}
        function Y() {}
        function X() {}
        function G() {}
        function Z() {}
        function J(e, t) {
          var n = [],
            r = (9 == this.nodeType && this.documentElement) || this,
            o = r.prefix,
            i = r.namespaceURI;
          if (i && null == o && null == (o = r.lookupPrefix(i)))
            var a = [{ namespace: i, prefix: null }];
          return (te(this, n, e, t, a), n.join(""));
        }
        function ee(e, t, n) {
          var r = e.prefix || "",
            o = e.namespaceURI;
          if (!r && !o) return !1;
          if (
            ("xml" === r && "http://www.w3.org/XML/1998/namespace" === o) ||
            "http://www.w3.org/2000/xmlns/" == o
          )
            return !1;
          for (var i = n.length; i--; ) {
            var a = n[i];
            if (a.prefix == r) return a.namespace != o;
          }
          return !0;
        }
        function te(e, t, n, r, i) {
          if (r) {
            if (!(e = r(e))) return;
            if ("string" == typeof e) return void t.push(e);
          }
          switch (e.nodeType) {
            case a:
              i || (i = []);
              i.length;
              var l = e.attributes,
                w = l.length,
                v = e.firstChild,
                b = e.tagName;
              ((n = o === e.namespaceURI || n), t.push("<", b));
              for (var y = 0; y < w; y++) {
                "xmlns" == (N = l.item(y)).prefix
                  ? i.push({ prefix: N.localName, namespace: N.value })
                  : "xmlns" == N.nodeName &&
                    i.push({ prefix: "", namespace: N.value });
              }
              for (y = 0; y < w; y++) {
                var N;
                if (ee((N = l.item(y)), 0, i)) {
                  var x = N.prefix || "",
                    E = N.namespaceURI,
                    O = x ? " xmlns:" + x : " xmlns";
                  (t.push(O, '="', E, '"'),
                    i.push({ prefix: x, namespace: E }));
                }
                te(N, t, n, r, i);
              }
              if (ee(e, 0, i)) {
                x = e.prefix || "";
                if ((E = e.namespaceURI)) {
                  O = x ? " xmlns:" + x : " xmlns";
                  (t.push(O, '="', E, '"'),
                    i.push({ prefix: x, namespace: E }));
                }
              }
              if (v || (n && !/^(?:meta|link|img|br|hr|input)$/i.test(b))) {
                if ((t.push(">"), n && /^script$/i.test(b)))
                  for (; v; )
                    (v.data ? t.push(v.data) : te(v, t, n, r, i),
                      (v = v.nextSibling));
                else for (; v; ) (te(v, t, n, r, i), (v = v.nextSibling));
                t.push("</", b, ">");
              } else t.push("/>");
              return;
            case m:
            case g:
              for (v = e.firstChild; v; )
                (te(v, t, n, r, i), (v = v.nextSibling));
              return;
            case s:
              return t.push(
                " ",
                e.name,
                '="',
                e.value.replace(/[<&"]/g, U),
                '"',
              );
            case d:
              return t.push(
                e.data.replace(/[<&]/g, U).replace(/]]>/g, "]]&gt;"),
              );
            case u:
              return t.push("<![CDATA[", e.data, "]]>");
            case p:
              return t.push("\x3c!--", e.data, "--\x3e");
            case h:
              var S = e.publicId,
                D = e.systemId;
              if ((t.push("<!DOCTYPE ", e.name), S))
                (t.push(" PUBLIC ", S),
                  D && "." != D && t.push(" ", D),
                  t.push(">"));
              else if (D && "." != D) t.push(" SYSTEM ", D, ">");
              else {
                var I = e.internalSubset;
                (I && t.push(" [", I, "]"), t.push(">"));
              }
              return;
            case f:
              return t.push("<?", e.target, " ", e.data, "?>");
            case c:
              return t.push("&", e.nodeName, ";");
            default:
              t.push("??", e.nodeName);
          }
        }
        function ne(e, t, n) {
          var r;
          switch (t.nodeType) {
            case a:
              (r = t.cloneNode(!1)).ownerDocument = e;
            case g:
              break;
            case s:
              n = !0;
          }
          if (
            (r || (r = t.cloneNode(!1)),
            (r.ownerDocument = e),
            (r.parentNode = null),
            n)
          )
            for (var o = t.firstChild; o; )
              (r.appendChild(ne(e, o, n)), (o = o.nextSibling));
          return r;
        }
        function re(e, t, n) {
          var r = new t.constructor();
          for (var o in t) {
            var i = t[o];
            "object" != typeof i && i != r[o] && (r[o] = i);
          }
          switch (
            (t.childNodes && (r.childNodes = new O()),
            (r.ownerDocument = e),
            r.nodeType)
          ) {
            case a:
              var d = t.attributes,
                u = (r.attributes = new I()),
                c = d.length;
              u._ownerElement = r;
              for (var l = 0; l < c; l++)
                r.setAttributeNode(re(e, d.item(l), !0));
              break;
            case s:
              n = !0;
          }
          if (n)
            for (var f = t.firstChild; f; )
              (r.appendChild(re(e, f, n)), (f = f.nextSibling));
          return r;
        }
        function oe(e, t, n) {
          e[t] = n;
        }
        ((E.prototype = Error.prototype),
          n(v, E),
          (O.prototype = {
            length: 0,
            item: function (e) {
              return this[e] || null;
            },
            toString: function (e, t) {
              for (var n = [], r = 0; r < this.length; r++)
                te(this[r], n, e, t);
              return n.join("");
            },
          }),
          (S.prototype.item = function (e) {
            return (D(this), this[e]);
          }),
          r(S, O),
          (I.prototype = {
            length: 0,
            item: O.prototype.item,
            getNamedItem: function (e) {
              for (var t = this.length; t--; ) {
                var n = this[t];
                if (n.nodeName == e) return n;
              }
            },
            setNamedItem: function (e) {
              var t = e.ownerElement;
              if (t && t != this._ownerElement) throw new E(x);
              var n = this.getNamedItem(e.nodeName);
              return (A(this._ownerElement, this, e, n), n);
            },
            setNamedItemNS: function (e) {
              var t,
                n = e.ownerElement;
              if (n && n != this._ownerElement) throw new E(x);
              return (
                (t = this.getNamedItemNS(e.namespaceURI, e.localName)),
                A(this._ownerElement, this, e, t),
                t
              );
            },
            removeNamedItem: function (e) {
              var t = this.getNamedItem(e);
              return (C(this._ownerElement, this, t), t);
            },
            removeNamedItemNS: function (e, t) {
              var n = this.getNamedItemNS(e, t);
              return (C(this._ownerElement, this, n), n);
            },
            getNamedItemNS: function (e, t) {
              for (var n = this.length; n--; ) {
                var r = this[n];
                if (r.localName == t && r.namespaceURI == e) return r;
              }
              return null;
            },
          }),
          (M.prototype = {
            hasFeature: function (e, t) {
              var n = this._features[e.toLowerCase()];
              return !(!n || (t && !(t in n)));
            },
            createDocument: function (e, t, n) {
              var r = new R();
              if (
                ((r.implementation = this),
                (r.childNodes = new O()),
                (r.doctype = n),
                n && r.appendChild(n),
                t)
              ) {
                var o = r.createElementNS(e, t);
                r.appendChild(o);
              }
              return r;
            },
            createDocumentType: function (e, t, n) {
              var r = new j();
              return (
                (r.name = e),
                (r.nodeName = e),
                (r.publicId = t),
                (r.systemId = n),
                r
              );
            },
          }),
          (_.prototype = {
            firstChild: null,
            lastChild: null,
            previousSibling: null,
            nextSibling: null,
            attributes: null,
            parentNode: null,
            childNodes: null,
            ownerDocument: null,
            nodeValue: null,
            namespaceURI: null,
            prefix: null,
            localName: null,
            insertBefore: function (e, t) {
              return F(this, e, t);
            },
            replaceChild: function (e, t) {
              (this.insertBefore(e, t), t && this.removeChild(t));
            },
            removeChild: function (e) {
              return P(this, e);
            },
            appendChild: function (e) {
              return this.insertBefore(e, null);
            },
            hasChildNodes: function () {
              return null != this.firstChild;
            },
            cloneNode: function (e) {
              return re(this.ownerDocument || this, this, e);
            },
            normalize: function () {
              for (var e = this.firstChild; e; ) {
                var t = e.nextSibling;
                t && t.nodeType == d && e.nodeType == d
                  ? (this.removeChild(t), e.appendData(t.data))
                  : (e.normalize(), (e = t));
              }
            },
            isSupported: function (e, t) {
              return this.ownerDocument.implementation.hasFeature(e, t);
            },
            hasAttributes: function () {
              return this.attributes.length > 0;
            },
            lookupPrefix: function (e) {
              for (var t = this; t; ) {
                var n = t._nsMap;
                if (n) for (var r in n) if (n[r] == e) return r;
                t = t.nodeType == s ? t.ownerDocument : t.parentNode;
              }
              return null;
            },
            lookupNamespaceURI: function (e) {
              for (var t = this; t; ) {
                var n = t._nsMap;
                if (n && e in n) return n[e];
                t = t.nodeType == s ? t.ownerDocument : t.parentNode;
              }
              return null;
            },
            isDefaultNamespace: function (e) {
              return null == this.lookupPrefix(e);
            },
          }),
          n(i, _),
          n(i, _.prototype),
          (R.prototype = {
            nodeName: "#document",
            nodeType: m,
            doctype: null,
            documentElement: null,
            _inc: 1,
            insertBefore: function (e, t) {
              if (e.nodeType == g) {
                for (var n = e.firstChild; n; ) {
                  var r = n.nextSibling;
                  (this.insertBefore(n, t), (n = r));
                }
                return e;
              }
              return (
                null == this.documentElement &&
                  e.nodeType == a &&
                  (this.documentElement = e),
                F(this, e, t),
                (e.ownerDocument = this),
                e
              );
            },
            removeChild: function (e) {
              return (
                this.documentElement == e && (this.documentElement = null),
                P(this, e)
              );
            },
            importNode: function (e, t) {
              return ne(this, e, t);
            },
            getElementById: function (e) {
              var t = null;
              return (
                $(this.documentElement, function (n) {
                  if (n.nodeType == a && n.getAttribute("id") == e)
                    return ((t = n), !0);
                }),
                t
              );
            },
            getElementsByClassName: function (e) {
              var t = new RegExp("(^|\\s)" + e + "(\\s|$)");
              return new S(this, function (e) {
                var n = [];
                return (
                  $(e.documentElement, function (r) {
                    r !== e &&
                      r.nodeType == a &&
                      t.test(r.getAttribute("class")) &&
                      n.push(r);
                  }),
                  n
                );
              });
            },
            createElement: function (e) {
              var t = new V();
              return (
                (t.ownerDocument = this),
                (t.nodeName = e),
                (t.tagName = e),
                (t.childNodes = new O()),
                ((t.attributes = new I())._ownerElement = t),
                t
              );
            },
            createDocumentFragment: function () {
              var e = new X();
              return ((e.ownerDocument = this), (e.childNodes = new O()), e);
            },
            createTextNode: function (e) {
              var t = new q();
              return ((t.ownerDocument = this), t.appendData(e), t);
            },
            createComment: function (e) {
              var t = new z();
              return ((t.ownerDocument = this), t.appendData(e), t);
            },
            createCDATASection: function (e) {
              var t = new Q();
              return ((t.ownerDocument = this), t.appendData(e), t);
            },
            createProcessingInstruction: function (e, t) {
              var n = new G();
              return (
                (n.ownerDocument = this),
                (n.tagName = n.target = e),
                (n.nodeValue = n.data = t),
                n
              );
            },
            createAttribute: function (e) {
              var t = new L();
              return (
                (t.ownerDocument = this),
                (t.name = e),
                (t.nodeName = e),
                (t.localName = e),
                (t.specified = !0),
                t
              );
            },
            createEntityReference: function (e) {
              var t = new Y();
              return ((t.ownerDocument = this), (t.nodeName = e), t);
            },
            createElementNS: function (e, t) {
              var n = new V(),
                r = t.split(":"),
                o = (n.attributes = new I());
              return (
                (n.childNodes = new O()),
                (n.ownerDocument = this),
                (n.nodeName = t),
                (n.tagName = t),
                (n.namespaceURI = e),
                2 == r.length
                  ? ((n.prefix = r[0]), (n.localName = r[1]))
                  : (n.localName = t),
                (o._ownerElement = n),
                n
              );
            },
            createAttributeNS: function (e, t) {
              var n = new L(),
                r = t.split(":");
              return (
                (n.ownerDocument = this),
                (n.nodeName = t),
                (n.name = t),
                (n.namespaceURI = e),
                (n.specified = !0),
                2 == r.length
                  ? ((n.prefix = r[0]), (n.localName = r[1]))
                  : (n.localName = t),
                n
              );
            },
          }),
          r(R, _),
          (V.prototype = {
            nodeType: a,
            hasAttribute: function (e) {
              return null != this.getAttributeNode(e);
            },
            getAttribute: function (e) {
              var t = this.getAttributeNode(e);
              return (t && t.value) || "";
            },
            getAttributeNode: function (e) {
              return this.attributes.getNamedItem(e);
            },
            setAttribute: function (e, t) {
              var n = this.ownerDocument.createAttribute(e);
              ((n.value = n.nodeValue = "" + t), this.setAttributeNode(n));
            },
            removeAttribute: function (e) {
              var t = this.getAttributeNode(e);
              t && this.removeAttributeNode(t);
            },
            appendChild: function (e) {
              return e.nodeType === g
                ? this.insertBefore(e, null)
                : (function (e, t) {
                    var n = t.parentNode;
                    if (n) {
                      var r = e.lastChild;
                      (n.removeChild(t), (r = e.lastChild));
                    }
                    return (
                      (r = e.lastChild),
                      (t.parentNode = e),
                      (t.previousSibling = r),
                      (t.nextSibling = null),
                      r ? (r.nextSibling = t) : (e.firstChild = t),
                      (e.lastChild = t),
                      B(e.ownerDocument, e, t),
                      t
                    );
                  })(this, e);
            },
            setAttributeNode: function (e) {
              return this.attributes.setNamedItem(e);
            },
            setAttributeNodeNS: function (e) {
              return this.attributes.setNamedItemNS(e);
            },
            removeAttributeNode: function (e) {
              return this.attributes.removeNamedItem(e.nodeName);
            },
            removeAttributeNS: function (e, t) {
              var n = this.getAttributeNodeNS(e, t);
              n && this.removeAttributeNode(n);
            },
            hasAttributeNS: function (e, t) {
              return null != this.getAttributeNodeNS(e, t);
            },
            getAttributeNS: function (e, t) {
              var n = this.getAttributeNodeNS(e, t);
              return (n && n.value) || "";
            },
            setAttributeNS: function (e, t, n) {
              var r = this.ownerDocument.createAttributeNS(e, t);
              ((r.value = r.nodeValue = "" + n), this.setAttributeNode(r));
            },
            getAttributeNodeNS: function (e, t) {
              return this.attributes.getNamedItemNS(e, t);
            },
            getElementsByTagName: function (e) {
              return new S(this, function (t) {
                var n = [];
                return (
                  $(t, function (r) {
                    r === t ||
                      r.nodeType != a ||
                      ("*" !== e && r.tagName != e) ||
                      n.push(r);
                  }),
                  n
                );
              });
            },
            getElementsByTagNameNS: function (e, t) {
              return new S(this, function (n) {
                var r = [];
                return (
                  $(n, function (o) {
                    o === n ||
                      o.nodeType !== a ||
                      ("*" !== e && o.namespaceURI !== e) ||
                      ("*" !== t && o.localName != t) ||
                      r.push(o);
                  }),
                  r
                );
              });
            },
          }),
          (R.prototype.getElementsByTagName = V.prototype.getElementsByTagName),
          (R.prototype.getElementsByTagNameNS =
            V.prototype.getElementsByTagNameNS),
          r(V, _),
          (L.prototype.nodeType = s),
          r(L, _),
          (W.prototype = {
            data: "",
            substringData: function (e, t) {
              return this.data.substring(e, e + t);
            },
            appendData: function (e) {
              ((e = this.data + e),
                (this.nodeValue = this.data = e),
                (this.length = e.length));
            },
            insertData: function (e, t) {
              this.replaceData(e, 0, t);
            },
            appendChild: function (e) {
              throw new Error(b[y]);
            },
            deleteData: function (e, t) {
              this.replaceData(e, t, "");
            },
            replaceData: function (e, t, n) {
              ((n = this.data.substring(0, e) + n + this.data.substring(e + t)),
                (this.nodeValue = this.data = n),
                (this.length = n.length));
            },
          }),
          r(W, _),
          (q.prototype = {
            nodeName: "#text",
            nodeType: d,
            splitText: function (e) {
              var t = this.data,
                n = t.substring(e);
              ((t = t.substring(0, e)),
                (this.data = this.nodeValue = t),
                (this.length = t.length));
              var r = this.ownerDocument.createTextNode(n);
              return (
                this.parentNode &&
                  this.parentNode.insertBefore(r, this.nextSibling),
                r
              );
            },
          }),
          r(q, W),
          (z.prototype = { nodeName: "#comment", nodeType: p }),
          r(z, W),
          (Q.prototype = { nodeName: "#cdata-section", nodeType: u }),
          r(Q, W),
          (j.prototype.nodeType = h),
          r(j, _),
          (H.prototype.nodeType = w),
          r(H, _),
          (K.prototype.nodeType = l),
          r(K, _),
          (Y.prototype.nodeType = c),
          r(Y, _),
          (X.prototype.nodeName = "#document-fragment"),
          (X.prototype.nodeType = g),
          r(X, _),
          (G.prototype.nodeType = f),
          r(G, _),
          (Z.prototype.serializeToString = function (e, t, n) {
            return J.call(e, t, n);
          }),
          (_.prototype.toString = J));
        try {
          if (Object.defineProperty) {
            function ie(e) {
              switch (e.nodeType) {
                case a:
                case g:
                  var t = [];
                  for (e = e.firstChild; e; )
                    (7 !== e.nodeType && 8 !== e.nodeType && t.push(ie(e)),
                      (e = e.nextSibling));
                  return t.join("");
                default:
                  return e.nodeValue;
              }
            }
            (Object.defineProperty(S.prototype, "length", {
              get: function () {
                return (D(this), this.$$length);
              },
            }),
              Object.defineProperty(_.prototype, "textContent", {
                get: function () {
                  return ie(this);
                },
                set: function (e) {
                  switch (this.nodeType) {
                    case a:
                    case g:
                      for (; this.firstChild; )
                        this.removeChild(this.firstChild);
                      (e || String(e)) &&
                        this.appendChild(this.ownerDocument.createTextNode(e));
                      break;
                    default:
                      ((this.data = e), (this.value = e), (this.nodeValue = e));
                  }
                },
              }),
              (oe = function (e, t, n) {
                e["$$" + t] = n;
              }));
          }
        } catch (ae) {}
        t.DOMImplementation = M;
      },
      791: (e, t) => {
        t.entityMap = {
          lt: "<",
          gt: ">",
          amp: "&",
          quot: '"',
          apos: "'",
          Agrave: "À",
          Aacute: "Á",
          Acirc: "Â",
          Atilde: "Ã",
          Auml: "Ä",
          Aring: "Å",
          AElig: "Æ",
          Ccedil: "Ç",
          Egrave: "È",
          Eacute: "É",
          Ecirc: "Ê",
          Euml: "Ë",
          Igrave: "Ì",
          Iacute: "Í",
          Icirc: "Î",
          Iuml: "Ï",
          ETH: "Ð",
          Ntilde: "Ñ",
          Ograve: "Ò",
          Oacute: "Ó",
          Ocirc: "Ô",
          Otilde: "Õ",
          Ouml: "Ö",
          Oslash: "Ø",
          Ugrave: "Ù",
          Uacute: "Ú",
          Ucirc: "Û",
          Uuml: "Ü",
          Yacute: "Ý",
          THORN: "Þ",
          szlig: "ß",
          agrave: "à",
          aacute: "á",
          acirc: "â",
          atilde: "ã",
          auml: "ä",
          aring: "å",
          aelig: "æ",
          ccedil: "ç",
          egrave: "è",
          eacute: "é",
          ecirc: "ê",
          euml: "ë",
          igrave: "ì",
          iacute: "í",
          icirc: "î",
          iuml: "ï",
          eth: "ð",
          ntilde: "ñ",
          ograve: "ò",
          oacute: "ó",
          ocirc: "ô",
          otilde: "õ",
          ouml: "ö",
          oslash: "ø",
          ugrave: "ù",
          uacute: "ú",
          ucirc: "û",
          uuml: "ü",
          yacute: "ý",
          thorn: "þ",
          yuml: "ÿ",
          nbsp: " ",
          iexcl: "¡",
          cent: "¢",
          pound: "£",
          curren: "¤",
          yen: "¥",
          brvbar: "¦",
          sect: "§",
          uml: "¨",
          copy: "©",
          ordf: "ª",
          laquo: "«",
          not: "¬",
          shy: "­­",
          reg: "®",
          macr: "¯",
          deg: "°",
          plusmn: "±",
          sup2: "²",
          sup3: "³",
          acute: "´",
          micro: "µ",
          para: "¶",
          middot: "·",
          cedil: "¸",
          sup1: "¹",
          ordm: "º",
          raquo: "»",
          frac14: "¼",
          frac12: "½",
          frac34: "¾",
          iquest: "¿",
          times: "×",
          divide: "÷",
          forall: "∀",
          part: "∂",
          exist: "∃",
          empty: "∅",
          nabla: "∇",
          isin: "∈",
          notin: "∉",
          ni: "∋",
          prod: "∏",
          sum: "∑",
          minus: "−",
          lowast: "∗",
          radic: "√",
          prop: "∝",
          infin: "∞",
          ang: "∠",
          and: "∧",
          or: "∨",
          cap: "∩",
          cup: "∪",
          int: "∫",
          there4: "∴",
          sim: "∼",
          cong: "≅",
          asymp: "≈",
          ne: "≠",
          equiv: "≡",
          le: "≤",
          ge: "≥",
          sub: "⊂",
          sup: "⊃",
          nsub: "⊄",
          sube: "⊆",
          supe: "⊇",
          oplus: "⊕",
          otimes: "⊗",
          perp: "⊥",
          sdot: "⋅",
          Alpha: "Α",
          Beta: "Β",
          Gamma: "Γ",
          Delta: "Δ",
          Epsilon: "Ε",
          Zeta: "Ζ",
          Eta: "Η",
          Theta: "Θ",
          Iota: "Ι",
          Kappa: "Κ",
          Lambda: "Λ",
          Mu: "Μ",
          Nu: "Ν",
          Xi: "Ξ",
          Omicron: "Ο",
          Pi: "Π",
          Rho: "Ρ",
          Sigma: "Σ",
          Tau: "Τ",
          Upsilon: "Υ",
          Phi: "Φ",
          Chi: "Χ",
          Psi: "Ψ",
          Omega: "Ω",
          alpha: "α",
          beta: "β",
          gamma: "γ",
          delta: "δ",
          epsilon: "ε",
          zeta: "ζ",
          eta: "η",
          theta: "θ",
          iota: "ι",
          kappa: "κ",
          lambda: "λ",
          mu: "μ",
          nu: "ν",
          xi: "ξ",
          omicron: "ο",
          pi: "π",
          rho: "ρ",
          sigmaf: "ς",
          sigma: "σ",
          tau: "τ",
          upsilon: "υ",
          phi: "φ",
          chi: "χ",
          psi: "ψ",
          omega: "ω",
          thetasym: "ϑ",
          upsih: "ϒ",
          piv: "ϖ",
          OElig: "Œ",
          oelig: "œ",
          Scaron: "Š",
          scaron: "š",
          Yuml: "Ÿ",
          fnof: "ƒ",
          circ: "ˆ",
          tilde: "˜",
          ensp: " ",
          emsp: " ",
          thinsp: " ",
          zwnj: "‌",
          zwj: "‍",
          lrm: "‎",
          rlm: "‏",
          ndash: "–",
          mdash: "—",
          lsquo: "‘",
          rsquo: "’",
          sbquo: "‚",
          ldquo: "“",
          rdquo: "”",
          bdquo: "„",
          dagger: "†",
          Dagger: "‡",
          bull: "•",
          hellip: "…",
          permil: "‰",
          prime: "′",
          Prime: "″",
          lsaquo: "‹",
          rsaquo: "›",
          oline: "‾",
          euro: "€",
          trade: "™",
          larr: "←",
          uarr: "↑",
          rarr: "→",
          darr: "↓",
          harr: "↔",
          crarr: "↵",
          lceil: "⌈",
          rceil: "⌉",
          lfloor: "⌊",
          rfloor: "⌋",
          loz: "◊",
          spades: "♠",
          clubs: "♣",
          hearts: "♥",
          diams: "♦",
        };
      },
      275: (e, t) => {
        var n =
            /[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,
          r = new RegExp(
            "[\\-\\.0-9" +
              n.source.slice(1, -1) +
              "\\u00B7\\u0300-\\u036F\\u203F-\\u2040]",
          ),
          o = new RegExp(
            "^" + n.source + r.source + "*(?::" + n.source + r.source + "*)?$",
          ),
          i = 0,
          a = 1,
          s = 2,
          d = 3,
          u = 4,
          c = 5,
          l = 6,
          f = 7;
        function p(e, t) {
          ((this.message = e),
            (this.locator = t),
            Error.captureStackTrace && Error.captureStackTrace(this, p));
        }
        function m() {}
        function h(e, t) {
          return (
            (t.lineNumber = e.lineNumber),
            (t.columnNumber = e.columnNumber),
            t
          );
        }
        function g(e, t, n, r, o, p) {
          function m(e, t, r) {
            (e in n.attributeNames &&
              p.fatalError("Attribute " + e + " redefined"),
              n.addValue(e, t, r));
          }
          for (var h, g = ++t, w = i; ; ) {
            var v = e.charAt(g);
            switch (v) {
              case "=":
                if (w === a) ((h = e.slice(t, g)), (w = d));
                else {
                  if (w !== s)
                    throw new Error("attribute equal must after attrName");
                  w = d;
                }
                break;
              case "'":
              case '"':
                if (w === d || w === a) {
                  if (
                    (w === a &&
                      (p.warning('attribute value must after "="'),
                      (h = e.slice(t, g))),
                    (t = g + 1),
                    !((g = e.indexOf(v, t)) > 0))
                  )
                    throw new Error("attribute value no end '" + v + "' match");
                  (m(h, (b = e.slice(t, g).replace(/&#?\w+;/g, o)), t - 1),
                    (w = c));
                } else {
                  if (w != u) throw new Error('attribute value must after "="');
                  (m(h, (b = e.slice(t, g).replace(/&#?\w+;/g, o)), t),
                    p.warning(
                      'attribute "' + h + '" missed start quot(' + v + ")!!",
                    ),
                    (t = g + 1),
                    (w = c));
                }
                break;
              case "/":
                switch (w) {
                  case i:
                    n.setTagName(e.slice(t, g));
                  case c:
                  case l:
                  case f:
                    ((w = f), (n.closed = !0));
                  case u:
                  case a:
                  case s:
                    break;
                  default:
                    throw new Error("attribute invalid close char('/')");
                }
                break;
              case "":
                return (
                  p.error("unexpected end of input"),
                  w == i && n.setTagName(e.slice(t, g)),
                  g
                );
              case ">":
                switch (w) {
                  case i:
                    n.setTagName(e.slice(t, g));
                  case c:
                  case l:
                  case f:
                    break;
                  case u:
                  case a:
                    "/" === (b = e.slice(t, g)).slice(-1) &&
                      ((n.closed = !0), (b = b.slice(0, -1)));
                  case s:
                    (w === s && (b = h),
                      w == u
                        ? (p.warning('attribute "' + b + '" missed quot(")!'),
                          m(h, b.replace(/&#?\w+;/g, o), t))
                        : (("http://www.w3.org/1999/xhtml" === r[""] &&
                            b.match(/^(?:disabled|checked|selected)$/i)) ||
                            p.warning(
                              'attribute "' +
                                b +
                                '" missed value!! "' +
                                b +
                                '" instead!!',
                            ),
                          m(b, b, t)));
                    break;
                  case d:
                    throw new Error("attribute value missed!!");
                }
                return g;
              case "":
                v = " ";
              default:
                if (v <= " ")
                  switch (w) {
                    case i:
                      (n.setTagName(e.slice(t, g)), (w = l));
                      break;
                    case a:
                      ((h = e.slice(t, g)), (w = s));
                      break;
                    case u:
                      var b = e.slice(t, g).replace(/&#?\w+;/g, o);
                      (p.warning('attribute "' + b + '" missed quot(")!!'),
                        m(h, b, t));
                    case c:
                      w = l;
                  }
                else
                  switch (w) {
                    case s:
                      n.tagName;
                      (("http://www.w3.org/1999/xhtml" === r[""] &&
                        h.match(/^(?:disabled|checked|selected)$/i)) ||
                        p.warning(
                          'attribute "' +
                            h +
                            '" missed value!! "' +
                            h +
                            '" instead2!!',
                        ),
                        m(h, h, t),
                        (t = g),
                        (w = a));
                      break;
                    case c:
                      p.warning('attribute space is required"' + h + '"!!');
                    case l:
                      ((w = a), (t = g));
                      break;
                    case d:
                      ((w = u), (t = g));
                      break;
                    case f:
                      throw new Error(
                        "elements closed character '/' and '>' must be connected to",
                      );
                  }
            }
            g++;
          }
        }
        function w(e, t, n) {
          for (var r = e.tagName, o = null, i = e.length; i--; ) {
            var a = e[i],
              s = a.qName,
              d = a.value;
            if ((f = s.indexOf(":")) > 0)
              var u = (a.prefix = s.slice(0, f)),
                c = s.slice(f + 1),
                l = "xmlns" === u && c;
            else ((c = s), (u = null), (l = "xmlns" === s && ""));
            ((a.localName = c),
              !1 !== l &&
                (null == o && ((o = {}), y(n, (n = {}))),
                (n[l] = o[l] = d),
                (a.uri = "http://www.w3.org/2000/xmlns/"),
                t.startPrefixMapping(l, d)));
          }
          for (i = e.length; i--; ) {
            (u = (a = e[i]).prefix) &&
              ("xml" === u && (a.uri = "http://www.w3.org/XML/1998/namespace"),
              "xmlns" !== u && (a.uri = n[u || ""]));
          }
          var f;
          (f = r.indexOf(":")) > 0
            ? ((u = e.prefix = r.slice(0, f)),
              (c = e.localName = r.slice(f + 1)))
            : ((u = null), (c = e.localName = r));
          var p = (e.uri = n[u || ""]);
          if ((t.startElement(p, c, r, e), !e.closed))
            return ((e.currentNSMap = n), (e.localNSMap = o), !0);
          if ((t.endElement(p, c, r), o)) for (u in o) t.endPrefixMapping(u);
        }
        function v(e, t, n, r, o) {
          if (/^(?:script|textarea)$/i.test(n)) {
            var i = e.indexOf("</" + n + ">", t),
              a = e.substring(t + 1, i);
            if (/[&<]/.test(a))
              return /^script$/i.test(n)
                ? (o.characters(a, 0, a.length), i)
                : ((a = a.replace(/&#?\w+;/g, r)),
                  o.characters(a, 0, a.length),
                  i);
          }
          return t + 1;
        }
        function b(e, t, n, r) {
          var o = r[n];
          return (
            null == o &&
              ((o = e.lastIndexOf("</" + n + ">")) < t &&
                (o = e.lastIndexOf("</" + n)),
              (r[n] = o)),
            o < t
          );
        }
        function y(e, t) {
          for (var n in e) t[n] = e[n];
        }
        function N(e, t, n, r) {
          if ("-" === e.charAt(t + 2))
            return "-" === e.charAt(t + 3)
              ? (o = e.indexOf("--\x3e", t + 4)) > t
                ? (n.comment(e, t + 4, o - t - 4), o + 3)
                : (r.error("Unclosed comment"), -1)
              : -1;
          if ("CDATA[" == e.substr(t + 3, 6)) {
            var o = e.indexOf("]]>", t + 9);
            return (
              n.startCDATA(),
              n.characters(e, t + 9, o - t - 9),
              n.endCDATA(),
              o + 3
            );
          }
          var i = (function (e, t) {
              var n,
                r = [],
                o = /'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;
              ((o.lastIndex = t), o.exec(e));
              for (; (n = o.exec(e)); ) if ((r.push(n), n[1])) return r;
            })(e, t),
            a = i.length;
          if (a > 1 && /!doctype/i.test(i[0][0])) {
            var s = i[1][0],
              d = !1,
              u = !1;
            a > 3 &&
              (/^public$/i.test(i[2][0])
                ? ((d = i[3][0]), (u = a > 4 && i[4][0]))
                : /^system$/i.test(i[2][0]) && (u = i[3][0]));
            var c = i[a - 1];
            return (n.startDTD(s, d, u), n.endDTD(), c.index + c[0].length);
          }
          return -1;
        }
        function x(e, t, n) {
          var r = e.indexOf("?>", t);
          if (r) {
            var o = e.substring(t, r).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);
            if (o) {
              o[0].length;
              return (n.processingInstruction(o[1], o[2]), r + 2);
            }
            return -1;
          }
          return -1;
        }
        function E() {
          this.attributeNames = {};
        }
        ((p.prototype = new Error()),
          (p.prototype.name = p.name),
          (m.prototype = {
            parse: function (e, t, n) {
              var r = this.domBuilder;
              (r.startDocument(),
                y(t, (t = {})),
                (function (e, t, n, r, o) {
                  function i(e) {
                    if (e > 65535) {
                      var t = 55296 + ((e -= 65536) >> 10),
                        n = 56320 + (1023 & e);
                      return String.fromCharCode(t, n);
                    }
                    return String.fromCharCode(e);
                  }
                  function a(e) {
                    var t = e.slice(1, -1);
                    return t in n
                      ? n[t]
                      : "#" === t.charAt(0)
                        ? i(parseInt(t.substr(1).replace("x", "0x")))
                        : (o.error("entity not found:" + e), e);
                  }
                  function s(t) {
                    if (t > O) {
                      var n = e.substring(O, t).replace(/&#?\w+;/g, a);
                      (f && d(O), r.characters(n, 0, t - O), (O = t));
                    }
                  }
                  function d(t, n) {
                    for (; t >= c && (n = l.exec(e)); )
                      ((u = n.index), (c = u + n[0].length), f.lineNumber++);
                    f.columnNumber = t - u + 1;
                  }
                  var u = 0,
                    c = 0,
                    l = /.*(?:\r\n?|\n)|.*$/g,
                    f = r.locator,
                    m = [{ currentNSMap: t }],
                    y = {},
                    O = 0;
                  for (;;) {
                    try {
                      var S = e.indexOf("<", O);
                      if (S < 0) {
                        if (!e.substr(O).match(/^\s*$/)) {
                          var D = r.doc,
                            I = D.createTextNode(e.substr(O));
                          (D.appendChild(I), (r.currentElement = I));
                        }
                        return;
                      }
                      switch ((S > O && s(S), e.charAt(S + 1))) {
                        case "/":
                          var T = e.indexOf(">", S + 3),
                            A = e.substring(S + 2, T),
                            C = m.pop();
                          T < 0
                            ? ((A = e.substring(S + 2).replace(/[\s<].*/, "")),
                              o.error(
                                "end tag name: " +
                                  A +
                                  " is not complete:" +
                                  C.tagName,
                              ),
                              (T = S + 1 + A.length))
                            : A.match(/\s</) &&
                              ((A = A.replace(/[\s<].*/, "")),
                              o.error(
                                "end tag name: " + A + " maybe not complete",
                              ),
                              (T = S + 1 + A.length));
                          var M = C.localNSMap,
                            _ = C.tagName == A;
                          if (
                            _ ||
                            (C.tagName &&
                              C.tagName.toLowerCase() == A.toLowerCase())
                          ) {
                            if ((r.endElement(C.uri, C.localName, A), M))
                              for (var U in M) r.endPrefixMapping(U);
                            _ ||
                              o.fatalError(
                                "end tag name: " +
                                  A +
                                  " is not match the current start tagName:" +
                                  C.tagName,
                              );
                          } else m.push(C);
                          T++;
                          break;
                        case "?":
                          (f && d(S), (T = x(e, S, r)));
                          break;
                        case "!":
                          (f && d(S), (T = N(e, S, r, o)));
                          break;
                        default:
                          f && d(S);
                          var $ = new E(),
                            R = m[m.length - 1].currentNSMap,
                            k = ((T = g(e, S, $, R, a, o)), $.length);
                          if (
                            (!$.closed &&
                              b(e, T, $.tagName, y) &&
                              (($.closed = !0),
                              n.nbsp || o.warning("unclosed xml attribute")),
                            f && k)
                          ) {
                            for (var B = h(f, {}), P = 0; P < k; P++) {
                              var F = $[P];
                              (d(F.offset), (F.locator = h(f, {})));
                            }
                            ((r.locator = B),
                              w($, r, R) && m.push($),
                              (r.locator = f));
                          } else w($, r, R) && m.push($);
                          "http://www.w3.org/1999/xhtml" !== $.uri || $.closed
                            ? T++
                            : (T = v(e, T, $.tagName, a, r));
                      }
                    } catch (e) {
                      if (e instanceof p) throw e;
                      (o.error("element parse error: " + e), (T = -1));
                    }
                    T > O ? (O = T) : s(Math.max(S, O) + 1);
                  }
                })(e, t, n, r, this.errorHandler),
                r.endDocument());
            },
          }),
          (E.prototype = {
            setTagName: function (e) {
              if (!o.test(e)) throw new Error("invalid tagName:" + e);
              this.tagName = e;
            },
            addValue: function (e, t, n) {
              if (!o.test(e)) throw new Error("invalid attribute:" + e);
              ((this.attributeNames[e] = this.length),
                (this[this.length++] = { qName: e, value: t, offset: n }));
            },
            length: 0,
            getLocalName: function (e) {
              return this[e].localName;
            },
            getLocator: function (e) {
              return this[e].locator;
            },
            getQName: function (e) {
              return this[e].qName;
            },
            getURI: function (e) {
              return this[e].uri;
            },
            getValue: function (e) {
              return this[e].value;
            },
          }),
          (t.XMLReader = m),
          (t.ParseError = p));
      },
    },
    t = {};
  function n(r) {
    var o = t[r];
    if (void 0 !== o) return o.exports;
    var i = (t[r] = { exports: {} });
    return (e[r](i, i.exports, n), i.exports);
  }
  (() => {
    "use strict";
    function e(e, t, n, r) {
      return new (n || (n = Promise))(function (o, i) {
        function a(e) {
          try {
            d(r.next(e));
          } catch (e) {
            i(e);
          }
        }
        function s(e) {
          try {
            d(r.throw(e));
          } catch (e) {
            i(e);
          }
        }
        function d(e) {
          var t;
          e.done
            ? o(e.value)
            : ((t = e.value),
              t instanceof n
                ? t
                : new n(function (e) {
                    e(t);
                  })).then(a, s);
        }
        d((r = r.apply(e, t || [])).next());
      });
    }
    Object.create;
    var t, r, o, i, a, s, d, u, c, l, f, p;
    Object.create;
    (!(function (e) {
      ((e[(e.pre = 0)] = "pre"),
        (e[(e.after = 1)] = "after"),
        (e[(e.getExtDrmKey = 2)] = "getExtDrmKey"));
    })(t || (t = {})),
      (function (e) {
        ((e[(e.single = 0)] = "single"),
          (e[(e.bulk = 1)] = "bulk"),
          (e[(e.bloburl = 2)] = "bloburl"),
          (e[(e.changeUrl = 3)] = "changeUrl"),
          (e[(e.login = 4)] = "login"),
          (e[(e.googleLogin = 5)] = "googleLogin"),
          (e[(e.register = 6)] = "register"),
          (e[(e.sendEmailCode = 7)] = "sendEmailCode"),
          (e[(e.getDrmSecretKey = 8)] = "getDrmSecretKey"),
          (e[(e.getConfig = 9)] = "getConfig"),
          (e[(e.getMemberInfo = 10)] = "getMemberInfo"),
          (e[(e.updateNoPerDayDownloadCount = 11)] =
            "updateNoPerDayDownloadCount"));
      })(r || (r = {})),
      (function (e) {
        ((e[(e.goSubscribe = 0)] = "goSubscribe"),
          (e[(e.pureNotice = 1)] = "pureNotice"),
          (e[(e.drmLicense = 2)] = "drmLicense"),
          (e[(e.retryMessage = 3)] = "retryMessage"),
          (e[(e.serverError = 4)] = "serverError"));
      })(o || (o = {})),
      (function (e) {
        ((e[(e.Edge = 0)] = "Edge"),
          (e[(e.Chrome = 1)] = "Chrome"),
          (e[(e.Firefox = 2)] = "Firefox"),
          (e[(e.Opera = 3)] = "Opera"),
          (e[(e.Safari = 4)] = "Safari"),
          (e[(e.Unknown = 5)] = "Unknown"));
      })(i || (i = {})),
      (function (e) {
        ((e.default = "log"), (e.warn = "warn"), (e.error = "error"));
      })(a || (a = {})),
      (function (e) {
        ((e.install = "install"),
          (e.uninstall = "uninstall"),
          (e.downloadSignalUnkown = "downloadSignalUnkown"),
          (e.downloadSignalImg = "downloadSignalImg"),
          (e.downloadSignalVideo = "downloadSignalVideo"),
          (e.downloadBulk = "downloadBulk"),
          (e.changeUrl = "changeUrl"),
          (e.register = "register"),
          (e.login = "login"),
          (e.googleLogin = "googleLogin"),
          (e.sendEmailCode = "sendEmailCode"),
          (e.uploadFiles = "uploadFiles"),
          (e.concatVideoAndAudio = "concatVideoAndAudio"));
      })(s || (s = {})),
      (function (e) {
        ((e.downloadSuccess = "downloadSuccess"),
          (e.downloadError = "downloadError"),
          (e.downloadCancle = "downloadCancle"),
          (e.downloadWating = "downloadWating"),
          (e.downloadPrepare = "downloadPrepare"),
          (e.downloadStuck = "downloadStuck"));
      })(d || (d = {})),
      (function (e) {
        ((e.addOrUpdateDownloadingInfo = "addOrUpdateDownloadingInfo"),
          (e.updateDownloadStatus = "updateDownloadStatus"));
      })(u || (u = {})),
      (function (e) {
        e[(e.refresh = 0)] = "refresh";
      })(c || (c = {})),
      (function (e) {
        ((e.downloading = "downloading"),
          (e.downloaded = "downloaded"),
          (e.download = "download"),
          (e.all = "all"),
          (e.quota = "quota"));
      })(l || (l = {})),
      (function (e) {
        ((e.processVideo = "processVideo"),
          (e.processVideoInWeb = "processVideoInWeb"),
          (e.processVideoByUrl = "processVideoByUrl"));
      })(f || (f = {})),
      (function (e) {
        ((e.serverError = "serverError"), (e.tip = "tip"));
      })(p || (p = {})));
    var m = n(647);
    class h {
      static waitFor(t) {
        return e(this, void 0, void 0, function* () {
          new Promise((e) => setTimeout(e, t));
        });
      }
      static asyncForEach(t, n) {
        return e(this, void 0, void 0, function* () {
          yield Promise.all(t.map((e, r) => n(e, r, t)));
        });
      }
      static getSingleVideo(t, n, r) {
        return e(this, void 0, void 0, function* () {
          const e = new URL(
              t + `/api-2.0/users/me/subscribed-courses/${n}/lectures/${r}`,
            ),
            o = {
              "fields[lecture]":
                "asset,description,download_url,is_free,last_watched_second",
              "fields[asset]":
                "asset_type,length,media_license_token,course_is_drmed,media_sources,stream_urls,captions,thumbnail_sprite,slides,slide_urls,download_urls,image_125_H",
            };
          Object.keys(o).forEach((t) => e.searchParams.append(t, o[t]));
          try {
            const t = yield fetch(e, {
              method: "GET",
              headers: { "Content-Type": "application/json" },
              credentials: "include",
            });
            if (!t.ok) {
              if (
                (console.log(`HTTP error! status: ${t.status}`),
                403 == t.status)
              )
                return { success: !1, error: "please login udemy first." };
              return { success: !1, error: yield t.json() };
            }
            return { success: !0, data: yield t.json() };
          } catch (e) {
            return (
              console.log("Error fetching video details:", e),
              { success: !1, error: e.message }
            );
          }
        });
      }
    }
    function g(t) {
      return e(this, void 0, void 0, function* () {
        let n = [],
          r = [];
        const o = yield (function (t) {
            return e(this, void 0, void 0, function* () {
              let e = "",
                n = "";
              if (null != t.mediaUrl && "" != t.mediaUrl) {
                const r = yield w(t.mediaUrl);
                if (r.success) ((n = ""), (e = r.data));
                else {
                  n = r.msg;
                  const o = yield v(t);
                  ((e = o.mpdContent), (n = o.errorMsg));
                }
              } else {
                const r = yield v(t);
                ((e = r.mpdContent), (n = r.errorMsg));
              }
              if ("" != n) throw new Error(n);
              return e;
            });
          })(t),
          i = new m.a().parseFromString(o, "application/xml"),
          a = "urn:mpeg:dash:schema:mpd:2011",
          { bestVideoId: s, bestAudioId: d } = yield (function (t, n) {
            return e(this, void 0, void 0, function* () {
              let e = null,
                r = null,
                o = 0,
                i = { width: 0, height: 0 };
              const a = t.getElementsByTagNameNS(n, "AdaptationSet");
              return (
                Array.from(a).forEach((t) => {
                  const a = t.getAttribute("mimeType");
                  if (a && a.includes("audio")) {
                    const r = t.getElementsByTagNameNS(n, "Representation");
                    Array.from(r).forEach((t) => {
                      const n = parseInt(t.getAttribute("bandwidth") || "0");
                      n > o && ((o = n), (e = t.getAttribute("id")));
                    });
                  } else if (a && a.includes("video")) {
                    const e = t.getElementsByTagNameNS(n, "Representation");
                    Array.from(e).forEach((e) => {
                      const t = parseInt(e.getAttribute("width") || "0"),
                        n = parseInt(e.getAttribute("height") || "0");
                      (t > i.width || n > i.height) &&
                        ((i = { width: t, height: n }),
                        (r = e.getAttribute("id")));
                    });
                  }
                }),
                { bestVideoId: r, bestAudioId: e }
              );
            });
          })(i, a),
          u = i.getElementsByTagNameNS(a, "AdaptationSet");
        return (
          Array.from(u).forEach((e) => {
            const t = e.getAttribute("mimeType");
            "video/mp4" === t
              ? (n = b(e, a, s))
              : "audio/mp4" === t && (r = b(e, a, d));
          }),
          { videoSegments: n, audioSegments: r }
        );
      });
    }
    function w(t) {
      return e(this, void 0, void 0, function* () {
        const e = yield fetch(t);
        if (e.ok) {
          return { success: !0, data: yield e.text() };
        }
        return { success: !1, msg: "can't fetch mpdcontent,please try again." };
      });
    }
    function v(t) {
      return e(this, void 0, void 0, function* () {
        let e = "",
          n = "";
        const r = yield h.getSingleVideo(t.webUrl, t.courseId, t.lectureId);
        if (r.success)
          if (
            r.data.asset.media_sources &&
            r.data.asset.media_sources.length > 0
          ) {
            let t = r.data.asset.media_sources[0].src;
            const o = yield w(t);
            o.success ? (e = o.data) : (n = o.msg);
          } else n = "can't found mediaUrl,please try again.";
        else n = r.error;
        return { mpdContent: e, errorMsg: n };
      });
    }
    function b(e, t, n) {
      const r = [],
        o = Array.from(e.getElementsByTagNameNS(t, "Representation")).find(
          (e) => e.getAttribute("id") === n,
        );
      if (o) {
        const e = o.getElementsByTagNameNS(t, "SegmentTemplate")[0];
        if (!e) throw new Error("SegmentTemplate element not found");
        const n = e.getAttribute("media"),
          i = e.getAttribute("initialization");
        r.push(i);
        const a = o.getElementsByTagNameNS(t, "SegmentTimeline")[0];
        if (a) {
          let e = 0;
          const o = a.getElementsByTagNameNS(t, "S");
          Array.from(o).forEach((t) => {
            const n = parseInt(t.getAttribute("r") || "0");
            e += n + 1;
          });
          for (let t = 0; t < e; t++) {
            const e = r.length,
              t = n.replace(/\$Number\$/, e);
            r.push(t);
          }
        }
      }
      return r;
    }
    var y,
      N,
      x = (function () {
        var e = "undefined" != typeof self ? self : this,
          t = {
            navigator: void 0 !== e.navigator ? e.navigator : {},
            infoMap: {
              engine: ["WebKit", "Trident", "Gecko", "Presto"],
              browser: [
                "Safari",
                "Chrome",
                "Edge",
                "IE",
                "Firefox",
                "Firefox Focus",
                "Chromium",
                "Opera",
                "Vivaldi",
                "Yandex",
                "Arora",
                "Lunascape",
                "QupZilla",
                "Coc Coc",
                "Kindle",
                "Iceweasel",
                "Konqueror",
                "Iceape",
                "SeaMonkey",
                "Epiphany",
                "360",
                "360SE",
                "360EE",
                "UC",
                "QQBrowser",
                "QQ",
                "Baidu",
                "Maxthon",
                "Sogou",
                "LBBROWSER",
                "2345Explorer",
                "TheWorld",
                "XiaoMi",
                "Quark",
                "Qiyu",
                "Wechat",
                ,
                "WechatWork",
                "Taobao",
                "Alipay",
                "Weibo",
                "Douban",
                "Suning",
                "iQiYi",
              ],
              os: [
                "Windows",
                "Linux",
                "Mac OS",
                "Android",
                "Ubuntu",
                "FreeBSD",
                "Debian",
                "iOS",
                "Windows Phone",
                "BlackBerry",
                "MeeGo",
                "Symbian",
                "Chrome OS",
                "WebOS",
              ],
              device: ["Mobile", "Tablet", "iPad"],
            },
          },
          n = {
            createUUID: function () {
              for (var e = [], t = "0123456789abcdef", n = 0; n < 36; n++)
                e[n] = t.substr(Math.floor(16 * Math.random()), 1);
              return (
                (e[14] = "4"),
                (e[19] = t.substr((3 & e[19]) | 8, 1)),
                (e[8] = e[13] = e[18] = e[23] = "-"),
                e.join("")
              );
            },
            getDate: function () {
              var e = new Date(),
                t = e.getFullYear(),
                n = e.getMonth() + 1,
                r = e.getDate(),
                o = e.getHours(),
                i = e.getMinutes(),
                a = e.getSeconds();
              return ""
                .concat(t.toString(), "/")
                .concat(n.toString(), "/")
                .concat(r.toString(), " ")
                .concat(o.toString(), ":")
                .concat(i.toString(), ":")
                .concat(a.toString());
            },
            getTimezoneOffset: function () {
              return new Date().getTimezoneOffset();
            },
            getTimezone: function () {
              return Intl.DateTimeFormat().resolvedOptions().timeZone;
            },
            getMatchMap: function (e) {
              return {
                Trident: e.indexOf("Trident") > -1 || e.indexOf("NET CLR") > -1,
                Presto: e.indexOf("Presto") > -1,
                WebKit: e.indexOf("AppleWebKit") > -1,
                Gecko: e.indexOf("Gecko/") > -1,
                Safari: e.indexOf("Safari") > -1,
                Chrome: e.indexOf("Chrome") > -1 || e.indexOf("CriOS") > -1,
                IE: e.indexOf("MSIE") > -1 || e.indexOf("Trident") > -1,
                Edge: e.indexOf("Edge") > -1,
                Firefox: e.indexOf("Firefox") > -1 || e.indexOf("FxiOS") > -1,
                "Firefox Focus": e.indexOf("Focus") > -1,
                Chromium: e.indexOf("Chromium") > -1,
                Opera: e.indexOf("Opera") > -1 || e.indexOf("OPR") > -1,
                Vivaldi: e.indexOf("Vivaldi") > -1,
                Yandex: e.indexOf("YaBrowser") > -1,
                Arora: e.indexOf("Arora") > -1,
                Lunascape: e.indexOf("Lunascape") > -1,
                QupZilla: e.indexOf("QupZilla") > -1,
                "Coc Coc": e.indexOf("coc_coc_browser") > -1,
                Kindle: e.indexOf("Kindle") > -1 || e.indexOf("Silk/") > -1,
                Iceweasel: e.indexOf("Iceweasel") > -1,
                Konqueror: e.indexOf("Konqueror") > -1,
                Iceape: e.indexOf("Iceape") > -1,
                SeaMonkey: e.indexOf("SeaMonkey") > -1,
                Epiphany: e.indexOf("Epiphany") > -1,
                360:
                  e.indexOf("QihooBrowser") > -1 || e.indexOf("QHBrowser") > -1,
                "360EE": e.indexOf("360EE") > -1,
                "360SE": e.indexOf("360SE") > -1,
                UC: e.indexOf("UC") > -1 || e.indexOf(" UBrowser") > -1,
                QQBrowser: e.indexOf("QQBrowser") > -1,
                QQ: e.indexOf("QQ/") > -1,
                Baidu: e.indexOf("Baidu") > -1 || e.indexOf("BIDUBrowser") > -1,
                Maxthon: e.indexOf("Maxthon") > -1,
                Sogou: e.indexOf("MetaSr") > -1 || e.indexOf("Sogou") > -1,
                LBBROWSER:
                  e.indexOf("LBBROWSER") > -1 || e.indexOf("LieBaoFast") > -1,
                "2345Explorer": e.indexOf("2345Explorer") > -1,
                TheWorld: e.indexOf("TheWorld") > -1,
                XiaoMi: e.indexOf("MiuiBrowser") > -1,
                Quark: e.indexOf("Quark") > -1,
                Qiyu: e.indexOf("Qiyu") > -1,
                Wechat: e.indexOf("MicroMessenger") > -1,
                WechatWork: e.indexOf("wxwork/") > -1,
                Taobao: e.indexOf("AliApp(TB") > -1,
                Alipay: e.indexOf("AliApp(AP") > -1,
                Weibo: e.indexOf("Weibo") > -1,
                Douban: e.indexOf("com.douban.frodo") > -1,
                Suning: e.indexOf("SNEBUY-APP") > -1,
                iQiYi: e.indexOf("IqiyiApp") > -1,
                DingTalk: e.indexOf("DingTalk") > -1,
                Vivo: e.indexOf("VivoBrowser") > -1,
                Huawei:
                  e.indexOf("HuaweiBrowser") > -1 ||
                  e.indexOf("HUAWEI/") > -1 ||
                  e.indexOf("HONOR") > -1 ||
                  e.indexOf("HBPC/") > -1,
                Windows: e.indexOf("Windows") > -1,
                Linux: e.indexOf("Linux") > -1 || e.indexOf("X11") > -1,
                "Mac OS": e.indexOf("Macintosh") > -1,
                Android: e.indexOf("Android") > -1 || e.indexOf("Adr") > -1,
                Ubuntu: e.indexOf("Ubuntu") > -1,
                FreeBSD: e.indexOf("FreeBSD") > -1,
                Debian: e.indexOf("Debian") > -1,
                "Windows Phone":
                  e.indexOf("IEMobile") > -1 || e.indexOf("Windows Phone") > -1,
                BlackBerry:
                  e.indexOf("BlackBerry") > -1 || e.indexOf("RIM") > -1,
                MeeGo: e.indexOf("MeeGo") > -1,
                Symbian: e.indexOf("Symbian") > -1,
                iOS: e.indexOf("like Mac OS X") > -1,
                "Chrome OS": e.indexOf("CrOS") > -1,
                WebOS: e.indexOf("hpwOS") > -1,
                Mobile:
                  e.indexOf("Mobi") > -1 ||
                  e.indexOf("iPh") > -1 ||
                  e.indexOf("480") > -1,
                Tablet: e.indexOf("Tablet") > -1 || e.indexOf("Nexus 7") > -1,
                iPad: e.indexOf("iPad") > -1,
              };
            },
            matchInfoMap: function (e) {
              var r = t.navigator.userAgent || {},
                o = n.getMatchMap(r);
              for (var i in t.infoMap)
                for (var a = 0; a < t.infoMap[i].length; a++) {
                  var s = t.infoMap[i][a];
                  o[s] && (e[i] = s);
                }
            },
            getOS: function () {
              return (n.matchInfoMap(this), this.os);
            },
            getOSVersion: function () {
              var e = this,
                n = t.navigator.userAgent || {};
              e.osVersion = "";
              var r = {
                Windows: function () {
                  var e = n.replace(/^.*Windows NT ([\d.]+);.*$/, "$1");
                  return (
                    {
                      10: "10 || 11",
                      6.3: "8.1",
                      6.2: "8",
                      6.1: "7",
                      "6.0": "Vista",
                      5.2: "XP 64-Bit",
                      5.1: "XP",
                      "5.0": "2000",
                      "4.0": "NT 4.0",
                      "3.5.1": "NT 3.5.1",
                      3.5: "NT 3.5",
                      3.1: "NT 3.1",
                    }[e] || e
                  );
                },
                Android: function () {
                  return n.replace(/^.*Android ([\d.]+);.*$/, "$1");
                },
                iOS: function () {
                  return n
                    .replace(/^.*OS ([\d_]+) like.*$/, "$1")
                    .replace(/_/g, ".");
                },
                Debian: function () {
                  return n.replace(/^.*Debian\/([\d.]+).*$/, "$1");
                },
                "Windows Phone": function () {
                  return n.replace(/^.*Windows Phone( OS)? ([\d.]+);.*$/, "$2");
                },
                "Mac OS": function () {
                  return n
                    .replace(/^.*Mac OS X ([\d_]+).*$/, "$1")
                    .replace(/_/g, ".");
                },
                WebOS: function () {
                  return n.replace(/^.*hpwOS\/([\d.]+);.*$/, "$1");
                },
              };
              return (
                r[e.os] &&
                  ((e.osVersion = r[e.os]()),
                  e.osVersion == n && (e.osVersion = "")),
                e.osVersion
              );
            },
            getDeviceType: function () {
              var e = this;
              return ((e.device = "PC"), n.matchInfoMap(e), e.device);
            },
            getNetwork: function () {
              return "";
            },
            getLanguage: function () {
              var e;
              return (
                (this.language =
                  ((e = (
                    t.navigator.browserLanguage || t.navigator.language
                  ).split("-"))[1] && (e[1] = e[1].toUpperCase()),
                  e.join("_"))),
                this.language
              );
            },
            getBrowserInfo: function () {
              var e = this;
              n.matchInfoMap(e);
              var r = t.navigator.userAgent || {},
                o = n.getMatchMap(r);
              if (
                (o.Baidu && o.Opera && (o.Baidu = !1),
                o.Mobile && (o.Mobile = !(r.indexOf("iPad") > -1)),
                o.IE || o.Edge)
              )
                switch (window.screenTop - window.screenY) {
                  case 71:
                  case 74:
                  case 99:
                  case 75:
                  case 74:
                  case 105:
                  default:
                    break;
                  case 102:
                    o["360EE"] = !0;
                    break;
                  case 104:
                    o["360SE"] = !0;
                }
              var i = {
                Safari: function () {
                  return r.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Chrome: function () {
                  return r
                    .replace(/^.*Chrome\/([\d.]+).*$/, "$1")
                    .replace(/^.*CriOS\/([\d.]+).*$/, "$1");
                },
                IE: function () {
                  return r
                    .replace(/^.*MSIE ([\d.]+).*$/, "$1")
                    .replace(/^.*rv:([\d.]+).*$/, "$1");
                },
                Edge: function () {
                  return r.replace(/^.*Edge\/([\d.]+).*$/, "$1");
                },
                Firefox: function () {
                  return r
                    .replace(/^.*Firefox\/([\d.]+).*$/, "$1")
                    .replace(/^.*FxiOS\/([\d.]+).*$/, "$1");
                },
                "Firefox Focus": function () {
                  return r.replace(/^.*Focus\/([\d.]+).*$/, "$1");
                },
                Chromium: function () {
                  return r.replace(/^.*Chromium\/([\d.]+).*$/, "$1");
                },
                Opera: function () {
                  return r
                    .replace(/^.*Opera\/([\d.]+).*$/, "$1")
                    .replace(/^.*OPR\/([\d.]+).*$/, "$1");
                },
                Vivaldi: function () {
                  return r.replace(/^.*Vivaldi\/([\d.]+).*$/, "$1");
                },
                Yandex: function () {
                  return r.replace(/^.*YaBrowser\/([\d.]+).*$/, "$1");
                },
                Arora: function () {
                  return r.replace(/^.*Arora\/([\d.]+).*$/, "$1");
                },
                Lunascape: function () {
                  return r.replace(/^.*Lunascape[\/\s]([\d.]+).*$/, "$1");
                },
                QupZilla: function () {
                  return r.replace(/^.*QupZilla[\/\s]([\d.]+).*$/, "$1");
                },
                "Coc Coc": function () {
                  return r.replace(/^.*coc_coc_browser\/([\d.]+).*$/, "$1");
                },
                Kindle: function () {
                  return r.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Iceweasel: function () {
                  return r.replace(/^.*Iceweasel\/([\d.]+).*$/, "$1");
                },
                Konqueror: function () {
                  return r.replace(/^.*Konqueror\/([\d.]+).*$/, "$1");
                },
                Iceape: function () {
                  return r.replace(/^.*Iceape\/([\d.]+).*$/, "$1");
                },
                SeaMonkey: function () {
                  return r.replace(/^.*SeaMonkey\/([\d.]+).*$/, "$1");
                },
                Epiphany: function () {
                  return r.replace(/^.*Epiphany\/([\d.]+).*$/, "$1");
                },
                Maxthon: function () {
                  return r.replace(/^.*Maxthon\/([\d.]+).*$/, "$1");
                },
              };
              return (
                (e.browserVersion = ""),
                i[e.browser] &&
                  ((e.browserVersion = i[e.browser]()),
                  e.browserVersion == r && (e.browserVersion = "")),
                "Chrome" == e.browser &&
                  r.match(/\S+Browser/) &&
                  ((e.browser = r.match(/\S+Browser/)[0]),
                  (e.version = r.replace(/^.*Browser\/([\d.]+).*$/, "$1"))),
                "Edge" == e.browser &&
                  (e.version > "75"
                    ? (e.engine = "Blink")
                    : (e.engine = "EdgeHTML")),
                (("Chrome" == e.browser && parseInt(e.browserVersion) > 27) ||
                  (o.Chrome &&
                    "WebKit" == e.engine &&
                    parseInt(i.Chrome()) > 27) ||
                  ("Opera" == e.browser && parseInt(e.version) > 12) ||
                  "Yandex" == e.browser) &&
                  (e.engine = "Blink"),
                e.browser +
                  "(version: " +
                  e.browserVersion +
                  "&nbsp;&nbsp;kernel: " +
                  e.engine +
                  ")"
              );
            },
            getGeoPostion: function () {
              return new Promise(function (e, t) {
                navigator && navigator.geolocation
                  ? navigator.geolocation.getCurrentPosition(
                      function (t) {
                        e(t);
                      },
                      function () {
                        e({ coords: { longitude: "fail", latitude: "fail" } });
                      },
                      { enableHighAccuracy: !1, timeout: 1e4 },
                    )
                  : t("fail");
              });
            },
            getPlatform: function () {
              return (
                (t.navigator.userAgentData &&
                  t.navigator.userAgentData.platform) ||
                t.navigator.platform
              );
            },
          },
          r = {
            DeviceInfoObj: function (e) {
              var r = {
                  deviceType: n.getDeviceType(),
                  os: n.getOS(),
                  osVersion: n.getOSVersion(),
                  platform: n.getPlatform(),
                  language: n.getLanguage(),
                  network: n.getNetwork(),
                  browserInfo: n.getBrowserInfo(),
                  userAgent: t.navigator.userAgent,
                  geoPosition: !0,
                  date: n.getDate(),
                  timezoneOffset: n.getTimezoneOffset(),
                  timezone: n.getTimezone(),
                  uuid: n.createUUID(),
                },
                o = {};
              if (e && e.info && 0 !== e.info.length) {
                var i = {},
                  a = function (t) {
                    e.info.forEach(function (e) {
                      e.toLowerCase() === t.toLowerCase() &&
                        (i[(e = t)] = r[e]);
                    });
                  };
                for (var s in r) a(s);
                o = i;
              } else o = r;
              return o;
            },
          };
        return {
          Info: function (e) {
            return r.DeviceInfoObj(e);
          },
        };
      })();
    function E() {
      return x.Info({
        info: [
          "deviceType",
          "OS",
          "OSVersion",
          "platform",
          "language",
          "netWork",
          "browserInfo",
          "screenHeight",
          "screenWidth",
          "userAgent",
          "appCodeName",
          "appName",
          "appVersion",
          "geoPosition",
          "date",
          "UUID",
          "timezoneOffset",
          "timezone",
        ],
      });
    }
    class O {
      static uploadFiles(t, n, r, o, i, a, s) {
        return e(this, void 0, void 0, function* () {
          const e = new FormData();
          let d = E();
          for (const t in d) e.append(t, d[t]);
          (e.append("userId", r),
            e.append("extId", o),
            e.append("version", i),
            e.append("action", a),
            e.append("detail", JSON.stringify(s)));
          for (let r = 0; r < t.length; r++) e.append("files", t[r], n[r]);
          try {
            const t = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/file/ude/" +
                d.uuid +
                "/upload?tk=" +
                d.uuid,
              { method: "POST", body: e },
            );
            if (!t.ok) {
              return { success: !1, error: yield t.json() };
            }
            return { success: !0, data: yield t.json() };
          } catch (e) {
            return { success: !1, error: e.message };
          }
        });
      }
      static uploadFilesWithProgress(t, n, r, o, i, a, s, d) {
        return e(this, void 0, void 0, function* () {
          const e = new FormData();
          let u = E();
          for (const t in u) e.append(t, u[t]);
          (e.append("userId", r),
            e.append("extId", o),
            e.append("version", i),
            e.append("action", a),
            e.append("detail", JSON.stringify(s)));
          for (let r = 0; r < t.length; r++) e.append("files", t[r], n[r]);
          return new Promise((t, n) => {
            const r = new XMLHttpRequest();
            (r.open(
              "POST",
              "https://www.bestaddons.store/ytbdserver/downloader/file/ude/upload?tk=" +
                u.uuid,
              !0,
            ),
              (r.upload.onprogress = (e) => {
                e.lengthComputable && d && d(e);
              }),
              (r.onload = () => {
                if (r.status >= 200 && r.status < 300) {
                  const e = JSON.parse(r.responseText);
                  t({ success: !0, data: e });
                } else {
                  const e = JSON.parse(r.responseText);
                  t({ success: !1, error: e });
                }
              }),
              (r.onerror = () => {
                n({
                  success: !1,
                  error: `Request failed with status ${r.status},status text: ${r.statusText}`,
                });
              }),
              r.send(e));
          });
        });
      }
      static concatVideoAndAudio(t, n, r, o, i, a) {
        return e(this, void 0, void 0, function* () {
          const e = new FormData();
          let s = E();
          for (const t in s) e.append(t, s[t]);
          (e.append("userId", t),
            e.append("extId", n),
            e.append("version", r),
            e.append("action", o),
            e.append("secretKey", a),
            e.append("detail", JSON.stringify(i)));
          try {
            const t = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/file/ude/concatVideoAndAudio",
              { method: "POST", body: e },
            );
            if (!t.ok) {
              return { success: !1, error: yield t.json() };
            }
            return { success: !0, data: yield t.json() };
          } catch (e) {
            return { success: !1, error: e.message };
          }
        });
      }
    }
    (!(function (e) {
      ((e.download = "download"),
        (e.progress = "progress"),
        (e.end = "end"),
        (e.cancle = "cancle"),
        (e.merge = "merge"),
        (e.error = "error"));
    })(y || (y = {})),
      (function (e) {
        ((e.downloadingToDownloaded = "downloadingToDownloaded"),
          (e.downloadToDownloaded = "downloadToDownloaded"));
      })(N || (N = {})));
    const S = {};
    function D(t, n, r) {
      return e(this, void 0, void 0, function* () {
        const e = yield fetch(t);
        let o = t.includes("udemybusiness");
        const i = e.body.getReader();
        let a = 0;
        const s = [];
        for (;;) {
          const { done: e, value: t } = yield i.read();
          if (e) {
            r && o && r();
            break;
          }
          (s.push(t), (a += t.byteLength), n && !o && n(t.byteLength));
        }
        const d = new Uint8Array(a);
        let u = 0;
        for (let e of s) (d.set(e, u), (u += e.length));
        return d.buffer;
      });
    }
    function I(t, n, r, o) {
      return e(this, void 0, void 0, function* () {
        let e = 0;
        const i = yield Promise.all(
          n.map((e) =>
            fetch(e, { method: "HEAD" })
              .then((e) => {
                const t = e.headers.get("Content-Length");
                return t ? +t : 0;
              })
              .catch(() => 0),
          ),
        ).then((e) => e.reduce((e, t) => e + t, 0));
        let a = yield Promise.all(
          n.map((t) =>
            D(
              t,
              (t) => {
                ((e += t), r && r(e, i));
              },
              o,
            ),
          ),
        );
        return (
          t && a.unshift(t),
          1 == a.length
            ? a[0]
            : (function (e) {
                const t = e.reduce((e, t) => e + t.byteLength, 0),
                  n = new Uint8Array(t);
                let r = 0;
                return (
                  e.forEach((e) => {
                    (n.set(new Uint8Array(e), r), (r += e.byteLength));
                  }),
                  n.buffer
                );
              })(a)
        );
      });
    }
    function T(t, n, r, o, i, a, s, d, u, c, l) {
      return e(this, void 0, void 0, function* () {
        let f = 0,
          p = 0,
          m = {};
        const h = new Set(),
          g = (e, t) => {
            if (((m["segment" + t] = e), l)) {
              let e = 0;
              for (const t of Object.values(m)) e += t;
              l((e + p) / n);
            }
          },
          w = (t, n) =>
            e(this, void 0, void 0, function* () {
              try {
                m["segment" + t] = 0;
                (yield A(n, t, i, a, s, d, g)).success ||
                  (yield _(2e3), yield A(n, t, i, a, s, d, g));
              } catch (e) {
                (yield _(2e3), yield A(n, t, i, a, s, d, g));
              } finally {
                (h.delete(t), p++, delete m["segment" + t]);
              }
            });
        for (let e = 0; e < n; e++) {
          let i = (e + 1) * t;
          i > r && (i = r);
          const a = o.slice(e * t, i),
            s = yield I(
              null,
              a,
              (t, r) => {
                const o = t / r;
                u && r > 0 && o <= 1 && u((o + e) / n);
              },
              () => {
                f++;
                c && c(f / r);
              },
            );
          if ((h.add(e), w(e, s), (e + 1) % 5 == 0 || e === n - 1))
            for (; h.size > 0; ) yield new Promise((e) => setTimeout(e, 100));
        }
      });
    }
    function A(t, n, r, o, i, a, d) {
      return e(this, void 0, void 0, function* () {
        let e = "video/mp4",
          u = ".mp4";
        return (
          "audio" == o && ((e = "audio/mp4"), (u = ".m4a")),
          yield O.uploadFilesWithProgress(
            [new Blob([new Uint8Array(t).buffer], { type: e })],
            [r + u],
            i,
            "a5376339-a50f-f096-248c-0e0cdde4f9af",
            a,
            s.uploadFiles,
            { courseId: r, type: o, segementIndex: n },
            (e) => {
              const t = e.loaded / e.total;
              d && d(t, n);
            },
          )
        );
      });
    }
    function C(t) {
      return e(this, void 0, void 0, function* () {
        let e = { action: y.progress, downloadStorageMessage: t };
        self.postMessage(e);
      });
    }
    function M(t) {
      return e(this, void 0, void 0, function* () {
        let e = { action: y.end, endMessage: t };
        self.postMessage(e);
      });
    }
    function _(e) {
      return new Promise((t) => setTimeout(t, e));
    }
    function U(t, n, r, o, i, a) {
      return e(this, void 0, void 0, function* () {
        let e = 0;
        const s = yield I(
          null,
          [o[0]],
          (e, t) => {
            const r = e / t;
            i && t > 0 && r <= 1 && i(r / (n + 1));
          },
          () => {
            e++;
            a && a(e / r);
          },
        );
        let d = [];
        for (let u = 0; u < n; u++) {
          let c = (u + 1) * t + 1;
          c > r && (c = r);
          const l = o.slice(u * t + 1, c),
            f = yield I(
              s,
              l,
              (e, t) => {
                const r = e / t;
                i && t > 0 && r <= 1 && i((r + u + 1) / (n + 1));
              },
              () => {
                e++;
                a && a(e / r);
              },
            );
          d.push(f);
        }
        return { initData: s, segmentsData: d };
      });
    }
    function $(t) {
      return e(this, void 0, void 0, function* () {
        let n = E(),
          r = {
            downloadId: t.downloadId,
            courseId: t.courseId,
            lectureId: t.lectureId,
            videoName: t.filenameOutput,
            downloadTime: n.date,
            secretKey: t.secretKey,
            percent: 0,
            msg: "Analyze media files...",
            webUrl: t.webUrl,
            downloadMethod: f.processVideoInWeb,
            videoSize: 0,
          };
        yield C({ action: u.addOrUpdateDownloadingInfo, data: r });
        let o = !0,
          i = "",
          [a, s] = [null, null];
        try {
          const n = [new AbortController(), new AbortController()],
            o = () => {},
            [i, d] = n;
          (i.signal.addEventListener("abort", o, { once: !0 }),
            d.signal.addEventListener("abort", o, { once: !0 }),
            (S[t.lectureId] = { isAborted: !1, abortControllers: n }));
          let { videoSegments: c, audioSegments: l } = yield g(t);
          ((r.percent = 2),
            (r.msg = "Analyze file size and prepare for download..."),
            yield C({ action: u.addOrUpdateDownloadingInfo, data: r }));
          let f = 0,
            p = 0;
          ((r.msg = "Download audio and video clips..."),
            yield C({ action: u.addOrUpdateDownloadingInfo, data: r }));
          const m = t.downloadSegmentsCount,
            h = Math.ceil((c.length - 1) / m),
            w = Math.ceil((l.length - 1) / m),
            v = () =>
              e(this, void 0, void 0, function* () {
                let e = (f + p) / 2;
                ((r.percent = parseFloat((83 * e + 2).toFixed(2))),
                  yield C({ action: u.addOrUpdateDownloadingInfo, data: r }));
              });
          (([a, s] = yield Promise.all([
            U(
              m,
              h,
              c.length,
              c,
              (e) => {
                ((f = e), v());
              },
              (e) => {
                ((f = e), v());
              },
            ),
            U(
              m,
              w,
              l.length,
              l,
              (e) => {
                ((p = e), v());
              },
              (e) => {
                ((p = e), v());
              },
            ),
          ])),
            (r.msg = "Merge videos, please wait for a moment..."),
            (r.videoSize = a.length + s.length),
            yield C({ action: u.addOrUpdateDownloadingInfo, data: r }));
        } catch (e) {
          (console.log(e), (o = !1), (i = e.error || e.toString()));
        } finally {
          if (o) {
            i = "";
            let n = {
              videoInitData: a.initData,
              audioInitData: s.initData,
              videoData: a.segmentsData,
              audioData: s.segmentsData,
            };
            (yield (function (t, n, r) {
              return e(this, void 0, void 0, function* () {
                try {
                  let e = {
                    action: y.merge,
                    workerDownloadMessage: t,
                    downloadStorageMessage: n,
                    mergeMessage: r,
                  };
                  self.postMessage(e, [...r.videoData, ...r.audioData]);
                } catch (e) {
                  console.log(e);
                  let o = e.error || e.toString();
                  ((n.msg = o), (r = null));
                  let i = {
                    action: y.error,
                    workerDownloadMessage: t,
                    downloadStorageMessage: n,
                  };
                  self.postMessage(i);
                }
              });
            })(t, { action: u.addOrUpdateDownloadingInfo, data: r }, n),
              (n = null));
          } else
            ((r.downloadMethod = f.processVideo),
              yield C({
                action: u.updateDownloadStatus,
                data: r,
                dowloadStatus: d.downloadError,
                msg: i,
              }),
              yield M({
                downloadId: r.downloadId,
                flag: o,
                errorMsg: i,
                url: "",
                filenameOutput: t.filenameOutput,
                mediaUrl: t.mediaUrl,
              }));
          ((a = null), (s = null));
        }
        return "";
      });
    }
    self.onmessage = (t) => {
      !(function (t) {
        e(this, void 0, void 0, function* () {
          t.downloadMethod == f.processVideo
            ? yield (function (t) {
                return e(this, void 0, void 0, function* () {
                  let n = "",
                    r = E(),
                    o = {
                      downloadId: t.downloadId,
                      courseId: t.courseId,
                      lectureId: t.lectureId,
                      videoName: t.filenameOutput,
                      downloadTime: r.date,
                      secretKey: t.secretKey,
                      downloadMethod: f.processVideo,
                      percent: 0,
                      msg: "Analyze media files...",
                      webUrl: t.webUrl,
                      videoSize: 0,
                    };
                  yield C({ action: u.addOrUpdateDownloadingInfo, data: o });
                  let i = !1,
                    a = "download fail.try again.";
                  try {
                    const r = [new AbortController(), new AbortController()],
                      d = () => {},
                      [c, l] = r;
                    (c.signal.addEventListener("abort", d, { once: !0 }),
                      l.signal.addEventListener("abort", d, { once: !0 }),
                      (S[t.lectureId] = {
                        isAborted: !1,
                        abortControllers: r,
                      }));
                    let { videoSegments: f, audioSegments: p } = yield g(t);
                    ((o.percent = 2),
                      (o.msg = "Analyze file size and prepare for download..."),
                      yield C({
                        action: u.addOrUpdateDownloadingInfo,
                        data: o,
                      }));
                    let m = 0,
                      h = 0,
                      w = 0,
                      v = 0;
                    ((o.msg = "Download audio and video clips..."),
                      yield C({
                        action: u.addOrUpdateDownloadingInfo,
                        data: o,
                      }));
                    const b = t.downloadSegmentsCount,
                      y = Math.ceil(f.length / b),
                      N = Math.ceil(p.length / b),
                      x = () =>
                        e(this, void 0, void 0, function* () {
                          let e = ((m + h) / 2) * 60,
                            t = ((w + v) / 2) * 40;
                          ((o.percent = parseFloat(
                            (0.96 * (e + t) + 2).toFixed(2),
                          )),
                            yield C({
                              action: u.addOrUpdateDownloadingInfo,
                              data: o,
                            }));
                        });
                    (yield Promise.all([
                      T(
                        b,
                        y,
                        f.length,
                        f,
                        t.lectureId,
                        "video",
                        t.userId,
                        t.version,
                        (e) => {
                          ((m = e), x());
                        },
                        (e) => {
                          ((m = e), x());
                        },
                        (e) => {
                          ((w = e), x());
                        },
                      ),
                      T(
                        b,
                        N,
                        p.length,
                        p,
                        t.lectureId,
                        "audio",
                        t.userId,
                        t.version,
                        (e) => {
                          ((h = e), x());
                        },
                        (e) => {
                          ((h = e), x());
                        },
                        (e) => {
                          ((v = e), x());
                        },
                      ),
                    ]),
                      (o.msg = "Merge videos, please wait for a moment..."),
                      yield C({
                        action: u.addOrUpdateDownloadingInfo,
                        data: o,
                      }));
                    let E = yield O.concatVideoAndAudio(
                      t.userId,
                      "a5376339-a50f-f096-248c-0e0cdde4f9af",
                      t.version,
                      s.concatVideoAndAudio,
                      {
                        courseId: t.lectureId,
                        courseName: t.filenameOutput,
                        videoSegementCount: y,
                        audioSegementCount: N,
                      },
                      t.secretKey,
                    );
                    if ((console.info(E), E.success))
                      if (0 == E.data.code) {
                        ((n =
                          "https://www.bestaddons.store/downloader/udemy/medias/a5376339-a50f-f096-248c-0e0cdde4f9af/" +
                          E.data.msg),
                          console.info(n));
                        const e =
                          "https://www.bestaddons.store/downloader/udemy/medias/a5376339-a50f-f096-248c-0e0cdde4f9af/" +
                          E.data.msg;
                        ((o.fileUrl = e), (i = !0));
                      } else
                        ((i = !1),
                          (a =
                            "Due to network or other reasons, the download fragment is incomplete. Please try again."));
                  } catch (e) {
                    (console.log(e), (i = !1), (a = e.error || e.toString()));
                  } finally {
                    (i
                      ? ((a = ""),
                        yield C({
                          action: u.updateDownloadStatus,
                          data: o,
                          dowloadStatus: d.downloadSuccess,
                        }))
                      : yield C({
                          action: u.updateDownloadStatus,
                          data: o,
                          dowloadStatus: d.downloadError,
                          msg: a,
                        }),
                      yield M({
                        downloadId: o.downloadId,
                        flag: i,
                        errorMsg: a,
                        url: n,
                        filenameOutput: t.filenameOutput,
                        mediaUrl: t.mediaUrl,
                      }));
                  }
                  return n;
                });
              })(t)
            : yield $(t);
        });
      })(t.data.workerDownloadMessage);
    };
  })();
})();
