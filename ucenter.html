<!doctype html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Udemy course video downloader online – UdemyFetcher™</title><style>body {
      font-family: Arial, sans-serif;
      background-color: #EFEBE0;
      color: #333;
      text-align: center;
      /* padding: 15px; */
      width: 550px;
      overflow-x: hidden;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      height: auto;
    }
    h1 {
      font-size: 18px;
      margin-bottom: 10px;
    }
    p {
      font-size: 14px;
      margin: 5px 0;
      color: #555;
    }
    .video-placeholder {
      display: inline-flex;
      align-items: center;
      
    }

    .video-placeholder img{
      width: 100%;
      margin: auto;
    }
    .help-section {
      margin-top: 40px;
    }
    .helptip{
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .button-row {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 10px;
    }
    .button {
      background-color: #fff;
      border: 2px solid #000;
      border-radius: 50px;
      padding: 10px 30px;
      display: flex;
      align-items: center;
      font-size: 16px;
      cursor: pointer;
      text-decoration: none;
      color: #000;
      width: 300px;
      text-align: left;
      height: 34px;
    }
    .button:hover {
      background-color: #f0f0f0;
    }
    .icon {
      margin-right: 10px;
    }
    .small-text {
      font-size: 14px;
      margin-top: 10px;
    }

    .bottom-text{
      margin-top: 30px;
    }

    .email{
      background-image: var(--addon-email);
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: center;
      display: block;
      cursor: pointer;
      width: 40px;
      height: 35px;
    }
    .discard{
      background-image: var(--addon-discard-green);
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: center;
      display: block;
      cursor: pointer;
      width: 40px;
      height: 35px;
    }

    #userId{
      font-size: 16px;
      font-weight: 700;
    }

    /* .buttonSpan{
      width: 140px;
    } */

    .sj-tip{
      font-size: 17px;
      color: red;
     }

     #GuestDiv,#MemberDiv,#ProMemberDiv{
      display: none !important;
     }

     .main-content {
       text-align: center;
       padding: 40px 20px;
       background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
       border-radius: 15px;
       margin: 20px 0;
       color: white;
       box-shadow: 0 8px 32px rgba(0,0,0,0.1);
     }

     .main-content h2 {
       margin: 0 0 10px 0;
       font-size: 24px;
       font-weight: 600;
     }

     .main-content p {
       margin: 0;
       opacity: 0.9;
       font-size: 16px;
     }</style></head><body><div class="container"><div class="video-placeholder"><img src="../assets/imgs/optionlogo.png"/></div><div class="main-content">
    <h2 style="color: #333; text-align: center;">Udemy Downloader</h2>
    <p style="text-align: center; color: #666;">Extension is ready to use</p>
</div>
</div></div><script src="js/option.js"></script></body></html>