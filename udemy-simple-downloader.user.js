// ==UserScript==
// @name         Udemy Simple Video Downloader
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  أداة بسيطة لتحميل فيديوهات Udemy
// <AUTHOR>
// @match        https://*.udemy.com/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    // إضافة الأنماط CSS
    const addStyles = () => {
        const style = document.createElement('style');
        style.textContent = `
            .udemy-downloader-panel {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 280px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                z-index: 999999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                color: white;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.1);
            }
            
            .udemy-downloader-header {
                padding: 15px;
                border-bottom: 1px solid rgba(255,255,255,0.1);
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .udemy-downloader-title {
                font-size: 16px;
                font-weight: 600;
                margin: 0;
            }
            
            .udemy-downloader-close {
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s ease;
            }
            
            .udemy-downloader-close:hover {
                background: rgba(255,255,255,0.3);
                transform: rotate(90deg);
            }
            
            .udemy-downloader-body {
                padding: 15px;
            }
            
            .udemy-download-btn {
                width: 100%;
                background: rgba(255,255,255,0.15);
                border: 1px solid rgba(255,255,255,0.2);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 10px;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
            }
            
            .udemy-download-btn:hover {
                background: rgba(255,255,255,0.25);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            }
            
            .udemy-download-btn:active {
                transform: translateY(0);
            }
            
            .udemy-download-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                transform: none;
            }
            
            .udemy-info-display {
                background: rgba(0,0,0,0.2);
                border-radius: 6px;
                padding: 10px;
                font-size: 12px;
                line-height: 1.4;
                margin-top: 10px;
                border-left: 3px solid #4ecdc4;
            }
            
            .udemy-status {
                font-size: 11px;
                opacity: 0.8;
                margin-top: 5px;
                text-align: center;
            }
            
            .udemy-toggle-btn {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                border-radius: 50%;
                color: white;
                font-size: 20px;
                cursor: pointer;
                z-index: 999998;
                box-shadow: 0 4px 16px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            }
            
            .udemy-toggle-btn:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 20px rgba(0,0,0,0.4);
            }
        `;
        document.head.appendChild(style);
    };
    
    // إنشاء واجهة المستخدم
    const createUI = () => {
        // زر التبديل
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'udemy-toggle-btn';
        toggleBtn.innerHTML = '⬇️';
        toggleBtn.title = 'Udemy Downloader';
        
        // اللوحة الرئيسية
        const panel = document.createElement('div');
        panel.className = 'udemy-downloader-panel';
        panel.style.display = 'none';
        
        panel.innerHTML = `
            <div class="udemy-downloader-header">
                <h3 class="udemy-downloader-title">🎬 Udemy Downloader</h3>
                <button class="udemy-downloader-close">×</button>
            </div>
            <div class="udemy-downloader-body">
                <button class="udemy-download-btn" id="download-video">
                    📹 Download Video
                </button>
                <button class="udemy-download-btn" id="get-video-info">
                    ℹ️ Video Info
                </button>
                <button class="udemy-download-btn" id="copy-video-url">
                    📋 Copy Video URL
                </button>
                <div id="info-display" class="udemy-info-display" style="display: none;"></div>
                <div class="udemy-status" id="status">Ready</div>
            </div>
        `;
        
        document.body.appendChild(toggleBtn);
        document.body.appendChild(panel);
        
        return { toggleBtn, panel };
    };
    
    // وظائف التحميل والمعلومات
    const videoFunctions = {
        findVideo: () => {
            return document.querySelector('video');
        },
        
        downloadVideo: () => {
            const video = videoFunctions.findVideo();
            const status = document.getElementById('status');
            
            if (!video) {
                status.textContent = '❌ No video found';
                return;
            }
            
            const videoSrc = video.src || video.currentSrc;
            if (!videoSrc) {
                status.textContent = '❌ Video source not available';
                return;
            }
            
            try {
                const link = document.createElement('a');
                link.href = videoSrc;
                link.download = `udemy-video-${Date.now()}.mp4`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                status.textContent = '✅ Download started';
            } catch (error) {
                status.textContent = '❌ Download failed';
                console.error('Download error:', error);
            }
        },
        
        getVideoInfo: () => {
            const video = videoFunctions.findVideo();
            const infoDisplay = document.getElementById('info-display');
            const status = document.getElementById('status');
            
            if (!video) {
                status.textContent = '❌ No video found';
                return;
            }
            
            const duration = video.duration;
            const currentTime = video.currentTime;
            const videoWidth = video.videoWidth;
            const videoHeight = video.videoHeight;
            const videoSrc = video.src || video.currentSrc;
            
            const formatTime = (seconds) => {
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins}:${secs.toString().padStart(2, '0')}`;
            };
            
            infoDisplay.innerHTML = `
                <strong>📊 Video Information:</strong><br>
                Duration: ${formatTime(duration)}<br>
                Current Time: ${formatTime(currentTime)}<br>
                Resolution: ${videoWidth}x${videoHeight}<br>
                Source: ${videoSrc ? 'Available' : 'Not found'}<br>
                Playback Rate: ${video.playbackRate}x
            `;
            
            infoDisplay.style.display = 'block';
            status.textContent = '✅ Info loaded';
        },
        
        copyVideoUrl: () => {
            const video = videoFunctions.findVideo();
            const status = document.getElementById('status');
            
            if (!video) {
                status.textContent = '❌ No video found';
                return;
            }
            
            const videoSrc = video.src || video.currentSrc;
            if (!videoSrc) {
                status.textContent = '❌ Video URL not available';
                return;
            }
            
            navigator.clipboard.writeText(videoSrc).then(() => {
                status.textContent = '✅ URL copied to clipboard';
            }).catch(() => {
                // Fallback للمتصفحات القديمة
                const textArea = document.createElement('textarea');
                textArea.value = videoSrc;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                status.textContent = '✅ URL copied to clipboard';
            });
        }
    };
    
    // تهيئة التطبيق
    const init = () => {
        addStyles();
        const { toggleBtn, panel } = createUI();
        
        // أحداث التبديل
        toggleBtn.addEventListener('click', () => {
            const isVisible = panel.style.display !== 'none';
            panel.style.display = isVisible ? 'none' : 'block';
            toggleBtn.style.display = isVisible ? 'block' : 'none';
        });
        
        // إغلاق اللوحة
        panel.querySelector('.udemy-downloader-close').addEventListener('click', () => {
            panel.style.display = 'none';
            toggleBtn.style.display = 'block';
        });
        
        // أحداث الأزرار
        document.getElementById('download-video').addEventListener('click', videoFunctions.downloadVideo);
        document.getElementById('get-video-info').addEventListener('click', videoFunctions.getVideoInfo);
        document.getElementById('copy-video-url').addEventListener('click', videoFunctions.copyVideoUrl);
        
        console.log('🎬 Udemy Simple Downloader loaded successfully!');
    };
    
    // تشغيل التطبيق عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();
