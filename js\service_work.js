(() => {
  var e = {
      733: (e) => {
        e.exports = (function e(t, r, n) {
          function i(s, a) {
            if (!r[s]) {
              if (!t[s]) {
                if (o) return o(s, !0);
                var d = new Error("Cannot find module '" + s + "'");
                throw ((d.code = "MODULE_NOT_FOUND"), d);
              }
              var l = (r[s] = { exports: {} });
              t[s][0].call(
                l.exports,
                function (e) {
                  return i(t[s][1][e] || e);
                },
                l,
                l.exports,
                e,
                t,
                r,
                n,
              );
            }
            return r[s].exports;
          }
          for (var o = void 0, s = 0; s < n.length; s++) i(n[s]);
          return i;
        })(
          {
            1: [
              function (e, t, r) {
                "use strict";
                var n = e("./utils"),
                  i = e("./support"),
                  o =
                    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
                ((r.encode = function (e) {
                  for (
                    var t,
                      r,
                      i,
                      s,
                      a,
                      d,
                      l,
                      c = [],
                      u = 0,
                      f = e.length,
                      h = f,
                      m = "string" !== n.getTypeOf(e);
                    u < e.length;

                  )
                    ((h = f - u),
                      (i = m
                        ? ((t = e[u++]),
                          (r = u < f ? e[u++] : 0),
                          u < f ? e[u++] : 0)
                        : ((t = e.charCodeAt(u++)),
                          (r = u < f ? e.charCodeAt(u++) : 0),
                          u < f ? e.charCodeAt(u++) : 0)),
                      (s = t >> 2),
                      (a = ((3 & t) << 4) | (r >> 4)),
                      (d = 1 < h ? ((15 & r) << 2) | (i >> 6) : 64),
                      (l = 2 < h ? 63 & i : 64),
                      c.push(
                        o.charAt(s) + o.charAt(a) + o.charAt(d) + o.charAt(l),
                      ));
                  return c.join("");
                }),
                  (r.decode = function (e) {
                    var t,
                      r,
                      n,
                      s,
                      a,
                      d,
                      l = 0,
                      c = 0,
                      u = "data:";
                    if (e.substr(0, u.length) === u)
                      throw new Error(
                        "Invalid base64 input, it looks like a data url.",
                      );
                    var f,
                      h =
                        (3 * (e = e.replace(/[^A-Za-z0-9+/=]/g, "")).length) /
                        4;
                    if (
                      (e.charAt(e.length - 1) === o.charAt(64) && h--,
                      e.charAt(e.length - 2) === o.charAt(64) && h--,
                      h % 1 != 0)
                    )
                      throw new Error(
                        "Invalid base64 input, bad content length.",
                      );
                    for (
                      f = i.uint8array
                        ? new Uint8Array(0 | h)
                        : new Array(0 | h);
                      l < e.length;

                    )
                      ((t =
                        (o.indexOf(e.charAt(l++)) << 2) |
                        ((s = o.indexOf(e.charAt(l++))) >> 4)),
                        (r =
                          ((15 & s) << 4) |
                          ((a = o.indexOf(e.charAt(l++))) >> 2)),
                        (n = ((3 & a) << 6) | (d = o.indexOf(e.charAt(l++)))),
                        (f[c++] = t),
                        64 !== a && (f[c++] = r),
                        64 !== d && (f[c++] = n));
                    return f;
                  }));
              },
              { "./support": 30, "./utils": 32 },
            ],
            2: [
              function (e, t, r) {
                "use strict";
                var n = e("./external"),
                  i = e("./stream/DataWorker"),
                  o = e("./stream/Crc32Probe"),
                  s = e("./stream/DataLengthProbe");
                function a(e, t, r, n, i) {
                  ((this.compressedSize = e),
                    (this.uncompressedSize = t),
                    (this.crc32 = r),
                    (this.compression = n),
                    (this.compressedContent = i));
                }
                ((a.prototype = {
                  getContentWorker: function () {
                    var e = new i(n.Promise.resolve(this.compressedContent))
                        .pipe(this.compression.uncompressWorker())
                        .pipe(new s("data_length")),
                      t = this;
                    return (
                      e.on("end", function () {
                        if (this.streamInfo.data_length !== t.uncompressedSize)
                          throw new Error(
                            "Bug : uncompressed data size mismatch",
                          );
                      }),
                      e
                    );
                  },
                  getCompressedWorker: function () {
                    return new i(n.Promise.resolve(this.compressedContent))
                      .withStreamInfo("compressedSize", this.compressedSize)
                      .withStreamInfo("uncompressedSize", this.uncompressedSize)
                      .withStreamInfo("crc32", this.crc32)
                      .withStreamInfo("compression", this.compression);
                  },
                }),
                  (a.createWorkerFrom = function (e, t, r) {
                    return e
                      .pipe(new o())
                      .pipe(new s("uncompressedSize"))
                      .pipe(t.compressWorker(r))
                      .pipe(new s("compressedSize"))
                      .withStreamInfo("compression", t);
                  }),
                  (t.exports = a));
              },
              {
                "./external": 6,
                "./stream/Crc32Probe": 25,
                "./stream/DataLengthProbe": 26,
                "./stream/DataWorker": 27,
              },
            ],
            3: [
              function (e, t, r) {
                "use strict";
                var n = e("./stream/GenericWorker");
                ((r.STORE = {
                  magic: "\0\0",
                  compressWorker: function () {
                    return new n("STORE compression");
                  },
                  uncompressWorker: function () {
                    return new n("STORE decompression");
                  },
                }),
                  (r.DEFLATE = e("./flate")));
              },
              { "./flate": 7, "./stream/GenericWorker": 28 },
            ],
            4: [
              function (e, t, r) {
                "use strict";
                var n = e("./utils"),
                  i = (function () {
                    for (var e, t = [], r = 0; r < 256; r++) {
                      e = r;
                      for (var n = 0; n < 8; n++)
                        e = 1 & e ? 3988292384 ^ (e >>> 1) : e >>> 1;
                      t[r] = e;
                    }
                    return t;
                  })();
                t.exports = function (e, t) {
                  return void 0 !== e && e.length
                    ? "string" !== n.getTypeOf(e)
                      ? (function (e, t, r, n) {
                          var o = i,
                            s = n + r;
                          e ^= -1;
                          for (var a = n; a < s; a++)
                            e = (e >>> 8) ^ o[255 & (e ^ t[a])];
                          return ~e;
                        })(0 | t, e, e.length, 0)
                      : (function (e, t, r, n) {
                          var o = i,
                            s = n + r;
                          e ^= -1;
                          for (var a = n; a < s; a++)
                            e = (e >>> 8) ^ o[255 & (e ^ t.charCodeAt(a))];
                          return ~e;
                        })(0 | t, e, e.length, 0)
                    : 0;
                };
              },
              { "./utils": 32 },
            ],
            5: [
              function (e, t, r) {
                "use strict";
                ((r.base64 = !1),
                  (r.binary = !1),
                  (r.dir = !1),
                  (r.createFolders = !0),
                  (r.date = null),
                  (r.compression = null),
                  (r.compressionOptions = null),
                  (r.comment = null),
                  (r.unixPermissions = null),
                  (r.dosPermissions = null));
              },
              {},
            ],
            6: [
              function (e, t, r) {
                "use strict";
                var n = null;
                ((n = "undefined" != typeof Promise ? Promise : e("lie")),
                  (t.exports = { Promise: n }));
              },
              { lie: 37 },
            ],
            7: [
              function (e, t, r) {
                "use strict";
                var n =
                    "undefined" != typeof Uint8Array &&
                    "undefined" != typeof Uint16Array &&
                    "undefined" != typeof Uint32Array,
                  i = e("pako"),
                  o = e("./utils"),
                  s = e("./stream/GenericWorker"),
                  a = n ? "uint8array" : "array";
                function d(e, t) {
                  (s.call(this, "FlateWorker/" + e),
                    (this._pako = null),
                    (this._pakoAction = e),
                    (this._pakoOptions = t),
                    (this.meta = {}));
                }
                ((r.magic = "\b\0"),
                  o.inherits(d, s),
                  (d.prototype.processChunk = function (e) {
                    ((this.meta = e.meta),
                      null === this._pako && this._createPako(),
                      this._pako.push(o.transformTo(a, e.data), !1));
                  }),
                  (d.prototype.flush = function () {
                    (s.prototype.flush.call(this),
                      null === this._pako && this._createPako(),
                      this._pako.push([], !0));
                  }),
                  (d.prototype.cleanUp = function () {
                    (s.prototype.cleanUp.call(this), (this._pako = null));
                  }),
                  (d.prototype._createPako = function () {
                    this._pako = new i[this._pakoAction]({
                      raw: !0,
                      level: this._pakoOptions.level || -1,
                    });
                    var e = this;
                    this._pako.onData = function (t) {
                      e.push({ data: t, meta: e.meta });
                    };
                  }),
                  (r.compressWorker = function (e) {
                    return new d("Deflate", e);
                  }),
                  (r.uncompressWorker = function () {
                    return new d("Inflate", {});
                  }));
              },
              { "./stream/GenericWorker": 28, "./utils": 32, pako: 38 },
            ],
            8: [
              function (e, t, r) {
                "use strict";
                function n(e, t) {
                  var r,
                    n = "";
                  for (r = 0; r < t; r++)
                    ((n += String.fromCharCode(255 & e)), (e >>>= 8));
                  return n;
                }
                function i(e, t, r, i, s, c) {
                  var u,
                    f,
                    h = e.file,
                    m = e.compression,
                    p = c !== a.utf8encode,
                    g = o.transformTo("string", c(h.name)),
                    w = o.transformTo("string", a.utf8encode(h.name)),
                    y = h.comment,
                    v = o.transformTo("string", c(y)),
                    b = o.transformTo("string", a.utf8encode(y)),
                    _ = w.length !== h.name.length,
                    x = b.length !== y.length,
                    A = "",
                    k = "",
                    S = "",
                    I = h.dir,
                    C = h.date,
                    O = { crc32: 0, compressedSize: 0, uncompressedSize: 0 };
                  (t && !r) ||
                    ((O.crc32 = e.crc32),
                    (O.compressedSize = e.compressedSize),
                    (O.uncompressedSize = e.uncompressedSize));
                  var E = 0;
                  (t && (E |= 8), p || (!_ && !x) || (E |= 2048));
                  var D = 0,
                    T = 0;
                  (I && (D |= 16),
                    "UNIX" === s
                      ? ((T = 798),
                        (D |= (function (e, t) {
                          var r = e;
                          return (
                            e || (r = t ? 16893 : 33204),
                            (65535 & r) << 16
                          );
                        })(h.unixPermissions, I)))
                      : ((T = 20),
                        (D |= (function (e) {
                          return 63 & (e || 0);
                        })(h.dosPermissions))),
                    (u = C.getUTCHours()),
                    (u <<= 6),
                    (u |= C.getUTCMinutes()),
                    (u <<= 5),
                    (u |= C.getUTCSeconds() / 2),
                    (f = C.getUTCFullYear() - 1980),
                    (f <<= 4),
                    (f |= C.getUTCMonth() + 1),
                    (f <<= 5),
                    (f |= C.getUTCDate()),
                    _ &&
                      ((k = n(1, 1) + n(d(g), 4) + w),
                      (A += "up" + n(k.length, 2) + k)),
                    x &&
                      ((S = n(1, 1) + n(d(v), 4) + b),
                      (A += "uc" + n(S.length, 2) + S)));
                  var U = "";
                  return (
                    (U += "\n\0"),
                    (U += n(E, 2)),
                    (U += m.magic),
                    (U += n(u, 2)),
                    (U += n(f, 2)),
                    (U += n(O.crc32, 4)),
                    (U += n(O.compressedSize, 4)),
                    (U += n(O.uncompressedSize, 4)),
                    (U += n(g.length, 2)),
                    (U += n(A.length, 2)),
                    {
                      fileRecord: l.LOCAL_FILE_HEADER + U + g + A,
                      dirRecord:
                        l.CENTRAL_FILE_HEADER +
                        n(T, 2) +
                        U +
                        n(v.length, 2) +
                        "\0\0\0\0" +
                        n(D, 4) +
                        n(i, 4) +
                        g +
                        A +
                        v,
                    }
                  );
                }
                var o = e("../utils"),
                  s = e("../stream/GenericWorker"),
                  a = e("../utf8"),
                  d = e("../crc32"),
                  l = e("../signature");
                function c(e, t, r, n) {
                  (s.call(this, "ZipFileWorker"),
                    (this.bytesWritten = 0),
                    (this.zipComment = t),
                    (this.zipPlatform = r),
                    (this.encodeFileName = n),
                    (this.streamFiles = e),
                    (this.accumulate = !1),
                    (this.contentBuffer = []),
                    (this.dirRecords = []),
                    (this.currentSourceOffset = 0),
                    (this.entriesCount = 0),
                    (this.currentFile = null),
                    (this._sources = []));
                }
                (o.inherits(c, s),
                  (c.prototype.push = function (e) {
                    var t = e.meta.percent || 0,
                      r = this.entriesCount,
                      n = this._sources.length;
                    this.accumulate
                      ? this.contentBuffer.push(e)
                      : ((this.bytesWritten += e.data.length),
                        s.prototype.push.call(this, {
                          data: e.data,
                          meta: {
                            currentFile: this.currentFile,
                            percent: r ? (t + 100 * (r - n - 1)) / r : 100,
                          },
                        }));
                  }),
                  (c.prototype.openedSource = function (e) {
                    ((this.currentSourceOffset = this.bytesWritten),
                      (this.currentFile = e.file.name));
                    var t = this.streamFiles && !e.file.dir;
                    if (t) {
                      var r = i(
                        e,
                        t,
                        !1,
                        this.currentSourceOffset,
                        this.zipPlatform,
                        this.encodeFileName,
                      );
                      this.push({ data: r.fileRecord, meta: { percent: 0 } });
                    } else this.accumulate = !0;
                  }),
                  (c.prototype.closedSource = function (e) {
                    this.accumulate = !1;
                    var t = this.streamFiles && !e.file.dir,
                      r = i(
                        e,
                        t,
                        !0,
                        this.currentSourceOffset,
                        this.zipPlatform,
                        this.encodeFileName,
                      );
                    if ((this.dirRecords.push(r.dirRecord), t))
                      this.push({
                        data: (function (e) {
                          return (
                            l.DATA_DESCRIPTOR +
                            n(e.crc32, 4) +
                            n(e.compressedSize, 4) +
                            n(e.uncompressedSize, 4)
                          );
                        })(e),
                        meta: { percent: 100 },
                      });
                    else
                      for (
                        this.push({ data: r.fileRecord, meta: { percent: 0 } });
                        this.contentBuffer.length;

                      )
                        this.push(this.contentBuffer.shift());
                    this.currentFile = null;
                  }),
                  (c.prototype.flush = function () {
                    for (
                      var e = this.bytesWritten, t = 0;
                      t < this.dirRecords.length;
                      t++
                    )
                      this.push({
                        data: this.dirRecords[t],
                        meta: { percent: 100 },
                      });
                    var r = this.bytesWritten - e,
                      i = (function (e, t, r, i, s) {
                        var a = o.transformTo("string", s(i));
                        return (
                          l.CENTRAL_DIRECTORY_END +
                          "\0\0\0\0" +
                          n(e, 2) +
                          n(e, 2) +
                          n(t, 4) +
                          n(r, 4) +
                          n(a.length, 2) +
                          a
                        );
                      })(
                        this.dirRecords.length,
                        r,
                        e,
                        this.zipComment,
                        this.encodeFileName,
                      );
                    this.push({ data: i, meta: { percent: 100 } });
                  }),
                  (c.prototype.prepareNextSource = function () {
                    ((this.previous = this._sources.shift()),
                      this.openedSource(this.previous.streamInfo),
                      this.isPaused
                        ? this.previous.pause()
                        : this.previous.resume());
                  }),
                  (c.prototype.registerPrevious = function (e) {
                    this._sources.push(e);
                    var t = this;
                    return (
                      e.on("data", function (e) {
                        t.processChunk(e);
                      }),
                      e.on("end", function () {
                        (t.closedSource(t.previous.streamInfo),
                          t._sources.length ? t.prepareNextSource() : t.end());
                      }),
                      e.on("error", function (e) {
                        t.error(e);
                      }),
                      this
                    );
                  }),
                  (c.prototype.resume = function () {
                    return (
                      !!s.prototype.resume.call(this) &&
                      (!this.previous && this._sources.length
                        ? (this.prepareNextSource(), !0)
                        : this.previous ||
                            this._sources.length ||
                            this.generatedError
                          ? void 0
                          : (this.end(), !0))
                    );
                  }),
                  (c.prototype.error = function (e) {
                    var t = this._sources;
                    if (!s.prototype.error.call(this, e)) return !1;
                    for (var r = 0; r < t.length; r++)
                      try {
                        t[r].error(e);
                      } catch (e) {}
                    return !0;
                  }),
                  (c.prototype.lock = function () {
                    s.prototype.lock.call(this);
                    for (var e = this._sources, t = 0; t < e.length; t++)
                      e[t].lock();
                  }),
                  (t.exports = c));
              },
              {
                "../crc32": 4,
                "../signature": 23,
                "../stream/GenericWorker": 28,
                "../utf8": 31,
                "../utils": 32,
              },
            ],
            9: [
              function (e, t, r) {
                "use strict";
                var n = e("../compressions"),
                  i = e("./ZipFileWorker");
                r.generateWorker = function (e, t, r) {
                  var o = new i(t.streamFiles, r, t.platform, t.encodeFileName),
                    s = 0;
                  try {
                    (e.forEach(function (e, r) {
                      s++;
                      var i = (function (e, t) {
                          var r = e || t,
                            i = n[r];
                          if (!i)
                            throw new Error(
                              r + " is not a valid compression method !",
                            );
                          return i;
                        })(r.options.compression, t.compression),
                        a =
                          r.options.compressionOptions ||
                          t.compressionOptions ||
                          {},
                        d = r.dir,
                        l = r.date;
                      r._compressWorker(i, a)
                        .withStreamInfo("file", {
                          name: e,
                          dir: d,
                          date: l,
                          comment: r.comment || "",
                          unixPermissions: r.unixPermissions,
                          dosPermissions: r.dosPermissions,
                        })
                        .pipe(o);
                    }),
                      (o.entriesCount = s));
                  } catch (e) {
                    o.error(e);
                  }
                  return o;
                };
              },
              { "../compressions": 3, "./ZipFileWorker": 8 },
            ],
            10: [
              function (e, t, r) {
                "use strict";
                function n() {
                  if (!(this instanceof n)) return new n();
                  if (arguments.length)
                    throw new Error(
                      "The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.",
                    );
                  ((this.files = Object.create(null)),
                    (this.comment = null),
                    (this.root = ""),
                    (this.clone = function () {
                      var e = new n();
                      for (var t in this)
                        "function" != typeof this[t] && (e[t] = this[t]);
                      return e;
                    }));
                }
                (((n.prototype = e("./object")).loadAsync = e("./load")),
                  (n.support = e("./support")),
                  (n.defaults = e("./defaults")),
                  (n.version = "3.10.1"),
                  (n.loadAsync = function (e, t) {
                    return new n().loadAsync(e, t);
                  }),
                  (n.external = e("./external")),
                  (t.exports = n));
              },
              {
                "./defaults": 5,
                "./external": 6,
                "./load": 11,
                "./object": 15,
                "./support": 30,
              },
            ],
            11: [
              function (e, t, r) {
                "use strict";
                var n = e("./utils"),
                  i = e("./external"),
                  o = e("./utf8"),
                  s = e("./zipEntries"),
                  a = e("./stream/Crc32Probe"),
                  d = e("./nodejsUtils");
                function l(e) {
                  return new i.Promise(function (t, r) {
                    var n = e.decompressed.getContentWorker().pipe(new a());
                    n.on("error", function (e) {
                      r(e);
                    })
                      .on("end", function () {
                        n.streamInfo.crc32 !== e.decompressed.crc32
                          ? r(new Error("Corrupted zip : CRC32 mismatch"))
                          : t();
                      })
                      .resume();
                  });
                }
                t.exports = function (e, t) {
                  var r = this;
                  return (
                    (t = n.extend(t || {}, {
                      base64: !1,
                      checkCRC32: !1,
                      optimizedBinaryString: !1,
                      createFolders: !1,
                      decodeFileName: o.utf8decode,
                    })),
                    d.isNode && d.isStream(e)
                      ? i.Promise.reject(
                          new Error(
                            "JSZip can't accept a stream when loading a zip file.",
                          ),
                        )
                      : n
                          .prepareContent(
                            "the loaded zip file",
                            e,
                            !0,
                            t.optimizedBinaryString,
                            t.base64,
                          )
                          .then(function (e) {
                            var r = new s(t);
                            return (r.load(e), r);
                          })
                          .then(function (e) {
                            var r = [i.Promise.resolve(e)],
                              n = e.files;
                            if (t.checkCRC32)
                              for (var o = 0; o < n.length; o++)
                                r.push(l(n[o]));
                            return i.Promise.all(r);
                          })
                          .then(function (e) {
                            for (
                              var i = e.shift(), o = i.files, s = 0;
                              s < o.length;
                              s++
                            ) {
                              var a = o[s],
                                d = a.fileNameStr,
                                l = n.resolve(a.fileNameStr);
                              (r.file(l, a.decompressed, {
                                binary: !0,
                                optimizedBinaryString: !0,
                                date: a.date,
                                dir: a.dir,
                                comment: a.fileCommentStr.length
                                  ? a.fileCommentStr
                                  : null,
                                unixPermissions: a.unixPermissions,
                                dosPermissions: a.dosPermissions,
                                createFolders: t.createFolders,
                              }),
                                a.dir || (r.file(l).unsafeOriginalName = d));
                            }
                            return (
                              i.zipComment.length && (r.comment = i.zipComment),
                              r
                            );
                          })
                  );
                };
              },
              {
                "./external": 6,
                "./nodejsUtils": 14,
                "./stream/Crc32Probe": 25,
                "./utf8": 31,
                "./utils": 32,
                "./zipEntries": 33,
              },
            ],
            12: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils"),
                  i = e("../stream/GenericWorker");
                function o(e, t) {
                  (i.call(this, "Nodejs stream input adapter for " + e),
                    (this._upstreamEnded = !1),
                    this._bindStream(t));
                }
                (n.inherits(o, i),
                  (o.prototype._bindStream = function (e) {
                    var t = this;
                    ((this._stream = e).pause(),
                      e
                        .on("data", function (e) {
                          t.push({ data: e, meta: { percent: 0 } });
                        })
                        .on("error", function (e) {
                          t.isPaused ? (this.generatedError = e) : t.error(e);
                        })
                        .on("end", function () {
                          t.isPaused ? (t._upstreamEnded = !0) : t.end();
                        }));
                  }),
                  (o.prototype.pause = function () {
                    return (
                      !!i.prototype.pause.call(this) &&
                      (this._stream.pause(), !0)
                    );
                  }),
                  (o.prototype.resume = function () {
                    return (
                      !!i.prototype.resume.call(this) &&
                      (this._upstreamEnded ? this.end() : this._stream.resume(),
                      !0)
                    );
                  }),
                  (t.exports = o));
              },
              { "../stream/GenericWorker": 28, "../utils": 32 },
            ],
            13: [
              function (e, t, r) {
                "use strict";
                var n = e("readable-stream").Readable;
                function i(e, t, r) {
                  (n.call(this, t), (this._helper = e));
                  var i = this;
                  e.on("data", function (e, t) {
                    (i.push(e) || i._helper.pause(), r && r(t));
                  })
                    .on("error", function (e) {
                      i.emit("error", e);
                    })
                    .on("end", function () {
                      i.push(null);
                    });
                }
                (e("../utils").inherits(i, n),
                  (i.prototype._read = function () {
                    this._helper.resume();
                  }),
                  (t.exports = i));
              },
              { "../utils": 32, "readable-stream": 16 },
            ],
            14: [
              function (e, t, r) {
                "use strict";
                t.exports = {
                  isNode: "undefined" != typeof Buffer,
                  newBufferFrom: function (e, t) {
                    if (Buffer.from && Buffer.from !== Uint8Array.from)
                      return Buffer.from(e, t);
                    if ("number" == typeof e)
                      throw new Error(
                        'The "data" argument must not be a number',
                      );
                    return new Buffer(e, t);
                  },
                  allocBuffer: function (e) {
                    if (Buffer.alloc) return Buffer.alloc(e);
                    var t = new Buffer(e);
                    return (t.fill(0), t);
                  },
                  isBuffer: function (e) {
                    return Buffer.isBuffer(e);
                  },
                  isStream: function (e) {
                    return (
                      e &&
                      "function" == typeof e.on &&
                      "function" == typeof e.pause &&
                      "function" == typeof e.resume
                    );
                  },
                };
              },
              {},
            ],
            15: [
              function (e, t, r) {
                "use strict";
                function n(e, t, r) {
                  var n,
                    i = o.getTypeOf(t),
                    a = o.extend(r || {}, d);
                  ((a.date = a.date || new Date()),
                    null !== a.compression &&
                      (a.compression = a.compression.toUpperCase()),
                    "string" == typeof a.unixPermissions &&
                      (a.unixPermissions = parseInt(a.unixPermissions, 8)),
                    a.unixPermissions &&
                      16384 & a.unixPermissions &&
                      (a.dir = !0),
                    a.dosPermissions && 16 & a.dosPermissions && (a.dir = !0),
                    a.dir && (e = p(e)),
                    a.createFolders && (n = m(e)) && g.call(this, n, !0));
                  var u = "string" === i && !1 === a.binary && !1 === a.base64;
                  ((r && void 0 !== r.binary) || (a.binary = !u),
                    ((t instanceof l && 0 === t.uncompressedSize) ||
                      a.dir ||
                      !t ||
                      0 === t.length) &&
                      ((a.base64 = !1),
                      (a.binary = !0),
                      (t = ""),
                      (a.compression = "STORE"),
                      (i = "string")));
                  var w = null;
                  w =
                    t instanceof l || t instanceof s
                      ? t
                      : f.isNode && f.isStream(t)
                        ? new h(e, t)
                        : o.prepareContent(
                            e,
                            t,
                            a.binary,
                            a.optimizedBinaryString,
                            a.base64,
                          );
                  var y = new c(e, w, a);
                  this.files[e] = y;
                }
                var i = e("./utf8"),
                  o = e("./utils"),
                  s = e("./stream/GenericWorker"),
                  a = e("./stream/StreamHelper"),
                  d = e("./defaults"),
                  l = e("./compressedObject"),
                  c = e("./zipObject"),
                  u = e("./generate"),
                  f = e("./nodejsUtils"),
                  h = e("./nodejs/NodejsStreamInputAdapter"),
                  m = function (e) {
                    "/" === e.slice(-1) && (e = e.substring(0, e.length - 1));
                    var t = e.lastIndexOf("/");
                    return 0 < t ? e.substring(0, t) : "";
                  },
                  p = function (e) {
                    return ("/" !== e.slice(-1) && (e += "/"), e);
                  },
                  g = function (e, t) {
                    return (
                      (t = void 0 !== t ? t : d.createFolders),
                      (e = p(e)),
                      this.files[e] ||
                        n.call(this, e, null, { dir: !0, createFolders: t }),
                      this.files[e]
                    );
                  };
                function w(e) {
                  return (
                    "[object RegExp]" === Object.prototype.toString.call(e)
                  );
                }
                var y = {
                  load: function () {
                    throw new Error(
                      "This method has been removed in JSZip 3.0, please check the upgrade guide.",
                    );
                  },
                  forEach: function (e) {
                    var t, r, n;
                    for (t in this.files)
                      ((n = this.files[t]),
                        (r = t.slice(this.root.length, t.length)) &&
                          t.slice(0, this.root.length) === this.root &&
                          e(r, n));
                  },
                  filter: function (e) {
                    var t = [];
                    return (
                      this.forEach(function (r, n) {
                        e(r, n) && t.push(n);
                      }),
                      t
                    );
                  },
                  file: function (e, t, r) {
                    if (1 !== arguments.length)
                      return ((e = this.root + e), n.call(this, e, t, r), this);
                    if (w(e)) {
                      var i = e;
                      return this.filter(function (e, t) {
                        return !t.dir && i.test(e);
                      });
                    }
                    var o = this.files[this.root + e];
                    return o && !o.dir ? o : null;
                  },
                  folder: function (e) {
                    if (!e) return this;
                    if (w(e))
                      return this.filter(function (t, r) {
                        return r.dir && e.test(t);
                      });
                    var t = this.root + e,
                      r = g.call(this, t),
                      n = this.clone();
                    return ((n.root = r.name), n);
                  },
                  remove: function (e) {
                    e = this.root + e;
                    var t = this.files[e];
                    if (
                      (t ||
                        ("/" !== e.slice(-1) && (e += "/"),
                        (t = this.files[e])),
                      t && !t.dir)
                    )
                      delete this.files[e];
                    else
                      for (
                        var r = this.filter(function (t, r) {
                            return r.name.slice(0, e.length) === e;
                          }),
                          n = 0;
                        n < r.length;
                        n++
                      )
                        delete this.files[r[n].name];
                    return this;
                  },
                  generate: function () {
                    throw new Error(
                      "This method has been removed in JSZip 3.0, please check the upgrade guide.",
                    );
                  },
                  generateInternalStream: function (e) {
                    var t,
                      r = {};
                    try {
                      if (
                        (((r = o.extend(e || {}, {
                          streamFiles: !1,
                          compression: "STORE",
                          compressionOptions: null,
                          type: "",
                          platform: "DOS",
                          comment: null,
                          mimeType: "application/zip",
                          encodeFileName: i.utf8encode,
                        })).type = r.type.toLowerCase()),
                        (r.compression = r.compression.toUpperCase()),
                        "binarystring" === r.type && (r.type = "string"),
                        !r.type)
                      )
                        throw new Error("No output type specified.");
                      (o.checkSupport(r.type),
                        ("darwin" !== r.platform &&
                          "freebsd" !== r.platform &&
                          "linux" !== r.platform &&
                          "sunos" !== r.platform) ||
                          (r.platform = "UNIX"),
                        "win32" === r.platform && (r.platform = "DOS"));
                      var n = r.comment || this.comment || "";
                      t = u.generateWorker(this, r, n);
                    } catch (e) {
                      (t = new s("error")).error(e);
                    }
                    return new a(t, r.type || "string", r.mimeType);
                  },
                  generateAsync: function (e, t) {
                    return this.generateInternalStream(e).accumulate(t);
                  },
                  generateNodeStream: function (e, t) {
                    return (
                      (e = e || {}).type || (e.type = "nodebuffer"),
                      this.generateInternalStream(e).toNodejsStream(t)
                    );
                  },
                };
                t.exports = y;
              },
              {
                "./compressedObject": 2,
                "./defaults": 5,
                "./generate": 9,
                "./nodejs/NodejsStreamInputAdapter": 12,
                "./nodejsUtils": 14,
                "./stream/GenericWorker": 28,
                "./stream/StreamHelper": 29,
                "./utf8": 31,
                "./utils": 32,
                "./zipObject": 35,
              },
            ],
            16: [
              function (e, t, r) {
                "use strict";
                t.exports = e("stream");
              },
              { stream: void 0 },
            ],
            17: [
              function (e, t, r) {
                "use strict";
                var n = e("./DataReader");
                function i(e) {
                  n.call(this, e);
                  for (var t = 0; t < this.data.length; t++) e[t] = 255 & e[t];
                }
                (e("../utils").inherits(i, n),
                  (i.prototype.byteAt = function (e) {
                    return this.data[this.zero + e];
                  }),
                  (i.prototype.lastIndexOfSignature = function (e) {
                    for (
                      var t = e.charCodeAt(0),
                        r = e.charCodeAt(1),
                        n = e.charCodeAt(2),
                        i = e.charCodeAt(3),
                        o = this.length - 4;
                      0 <= o;
                      --o
                    )
                      if (
                        this.data[o] === t &&
                        this.data[o + 1] === r &&
                        this.data[o + 2] === n &&
                        this.data[o + 3] === i
                      )
                        return o - this.zero;
                    return -1;
                  }),
                  (i.prototype.readAndCheckSignature = function (e) {
                    var t = e.charCodeAt(0),
                      r = e.charCodeAt(1),
                      n = e.charCodeAt(2),
                      i = e.charCodeAt(3),
                      o = this.readData(4);
                    return t === o[0] && r === o[1] && n === o[2] && i === o[3];
                  }),
                  (i.prototype.readData = function (e) {
                    if ((this.checkOffset(e), 0 === e)) return [];
                    var t = this.data.slice(
                      this.zero + this.index,
                      this.zero + this.index + e,
                    );
                    return ((this.index += e), t);
                  }),
                  (t.exports = i));
              },
              { "../utils": 32, "./DataReader": 18 },
            ],
            18: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils");
                function i(e) {
                  ((this.data = e),
                    (this.length = e.length),
                    (this.index = 0),
                    (this.zero = 0));
                }
                ((i.prototype = {
                  checkOffset: function (e) {
                    this.checkIndex(this.index + e);
                  },
                  checkIndex: function (e) {
                    if (this.length < this.zero + e || e < 0)
                      throw new Error(
                        "End of data reached (data length = " +
                          this.length +
                          ", asked index = " +
                          e +
                          "). Corrupted zip ?",
                      );
                  },
                  setIndex: function (e) {
                    (this.checkIndex(e), (this.index = e));
                  },
                  skip: function (e) {
                    this.setIndex(this.index + e);
                  },
                  byteAt: function () {},
                  readInt: function (e) {
                    var t,
                      r = 0;
                    for (
                      this.checkOffset(e), t = this.index + e - 1;
                      t >= this.index;
                      t--
                    )
                      r = (r << 8) + this.byteAt(t);
                    return ((this.index += e), r);
                  },
                  readString: function (e) {
                    return n.transformTo("string", this.readData(e));
                  },
                  readData: function () {},
                  lastIndexOfSignature: function () {},
                  readAndCheckSignature: function () {},
                  readDate: function () {
                    var e = this.readInt(4);
                    return new Date(
                      Date.UTC(
                        1980 + ((e >> 25) & 127),
                        ((e >> 21) & 15) - 1,
                        (e >> 16) & 31,
                        (e >> 11) & 31,
                        (e >> 5) & 63,
                        (31 & e) << 1,
                      ),
                    );
                  },
                }),
                  (t.exports = i));
              },
              { "../utils": 32 },
            ],
            19: [
              function (e, t, r) {
                "use strict";
                var n = e("./Uint8ArrayReader");
                function i(e) {
                  n.call(this, e);
                }
                (e("../utils").inherits(i, n),
                  (i.prototype.readData = function (e) {
                    this.checkOffset(e);
                    var t = this.data.slice(
                      this.zero + this.index,
                      this.zero + this.index + e,
                    );
                    return ((this.index += e), t);
                  }),
                  (t.exports = i));
              },
              { "../utils": 32, "./Uint8ArrayReader": 21 },
            ],
            20: [
              function (e, t, r) {
                "use strict";
                var n = e("./DataReader");
                function i(e) {
                  n.call(this, e);
                }
                (e("../utils").inherits(i, n),
                  (i.prototype.byteAt = function (e) {
                    return this.data.charCodeAt(this.zero + e);
                  }),
                  (i.prototype.lastIndexOfSignature = function (e) {
                    return this.data.lastIndexOf(e) - this.zero;
                  }),
                  (i.prototype.readAndCheckSignature = function (e) {
                    return e === this.readData(4);
                  }),
                  (i.prototype.readData = function (e) {
                    this.checkOffset(e);
                    var t = this.data.slice(
                      this.zero + this.index,
                      this.zero + this.index + e,
                    );
                    return ((this.index += e), t);
                  }),
                  (t.exports = i));
              },
              { "../utils": 32, "./DataReader": 18 },
            ],
            21: [
              function (e, t, r) {
                "use strict";
                var n = e("./ArrayReader");
                function i(e) {
                  n.call(this, e);
                }
                (e("../utils").inherits(i, n),
                  (i.prototype.readData = function (e) {
                    if ((this.checkOffset(e), 0 === e))
                      return new Uint8Array(0);
                    var t = this.data.subarray(
                      this.zero + this.index,
                      this.zero + this.index + e,
                    );
                    return ((this.index += e), t);
                  }),
                  (t.exports = i));
              },
              { "../utils": 32, "./ArrayReader": 17 },
            ],
            22: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils"),
                  i = e("../support"),
                  o = e("./ArrayReader"),
                  s = e("./StringReader"),
                  a = e("./NodeBufferReader"),
                  d = e("./Uint8ArrayReader");
                t.exports = function (e) {
                  var t = n.getTypeOf(e);
                  return (
                    n.checkSupport(t),
                    "string" !== t || i.uint8array
                      ? "nodebuffer" === t
                        ? new a(e)
                        : i.uint8array
                          ? new d(n.transformTo("uint8array", e))
                          : new o(n.transformTo("array", e))
                      : new s(e)
                  );
                };
              },
              {
                "../support": 30,
                "../utils": 32,
                "./ArrayReader": 17,
                "./NodeBufferReader": 19,
                "./StringReader": 20,
                "./Uint8ArrayReader": 21,
              },
            ],
            23: [
              function (e, t, r) {
                "use strict";
                ((r.LOCAL_FILE_HEADER = "PK"),
                  (r.CENTRAL_FILE_HEADER = "PK"),
                  (r.CENTRAL_DIRECTORY_END = "PK"),
                  (r.ZIP64_CENTRAL_DIRECTORY_LOCATOR = "PK"),
                  (r.ZIP64_CENTRAL_DIRECTORY_END = "PK"),
                  (r.DATA_DESCRIPTOR = "PK\b"));
              },
              {},
            ],
            24: [
              function (e, t, r) {
                "use strict";
                var n = e("./GenericWorker"),
                  i = e("../utils");
                function o(e) {
                  (n.call(this, "ConvertWorker to " + e), (this.destType = e));
                }
                (i.inherits(o, n),
                  (o.prototype.processChunk = function (e) {
                    this.push({
                      data: i.transformTo(this.destType, e.data),
                      meta: e.meta,
                    });
                  }),
                  (t.exports = o));
              },
              { "../utils": 32, "./GenericWorker": 28 },
            ],
            25: [
              function (e, t, r) {
                "use strict";
                var n = e("./GenericWorker"),
                  i = e("../crc32");
                function o() {
                  (n.call(this, "Crc32Probe"), this.withStreamInfo("crc32", 0));
                }
                (e("../utils").inherits(o, n),
                  (o.prototype.processChunk = function (e) {
                    ((this.streamInfo.crc32 = i(
                      e.data,
                      this.streamInfo.crc32 || 0,
                    )),
                      this.push(e));
                  }),
                  (t.exports = o));
              },
              { "../crc32": 4, "../utils": 32, "./GenericWorker": 28 },
            ],
            26: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils"),
                  i = e("./GenericWorker");
                function o(e) {
                  (i.call(this, "DataLengthProbe for " + e),
                    (this.propName = e),
                    this.withStreamInfo(e, 0));
                }
                (n.inherits(o, i),
                  (o.prototype.processChunk = function (e) {
                    if (e) {
                      var t = this.streamInfo[this.propName] || 0;
                      this.streamInfo[this.propName] = t + e.data.length;
                    }
                    i.prototype.processChunk.call(this, e);
                  }),
                  (t.exports = o));
              },
              { "../utils": 32, "./GenericWorker": 28 },
            ],
            27: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils"),
                  i = e("./GenericWorker");
                function o(e) {
                  i.call(this, "DataWorker");
                  var t = this;
                  ((this.dataIsReady = !1),
                    (this.index = 0),
                    (this.max = 0),
                    (this.data = null),
                    (this.type = ""),
                    (this._tickScheduled = !1),
                    e.then(
                      function (e) {
                        ((t.dataIsReady = !0),
                          (t.data = e),
                          (t.max = (e && e.length) || 0),
                          (t.type = n.getTypeOf(e)),
                          t.isPaused || t._tickAndRepeat());
                      },
                      function (e) {
                        t.error(e);
                      },
                    ));
                }
                (n.inherits(o, i),
                  (o.prototype.cleanUp = function () {
                    (i.prototype.cleanUp.call(this), (this.data = null));
                  }),
                  (o.prototype.resume = function () {
                    return (
                      !!i.prototype.resume.call(this) &&
                      (!this._tickScheduled &&
                        this.dataIsReady &&
                        ((this._tickScheduled = !0),
                        n.delay(this._tickAndRepeat, [], this)),
                      !0)
                    );
                  }),
                  (o.prototype._tickAndRepeat = function () {
                    ((this._tickScheduled = !1),
                      this.isPaused ||
                        this.isFinished ||
                        (this._tick(),
                        this.isFinished ||
                          (n.delay(this._tickAndRepeat, [], this),
                          (this._tickScheduled = !0))));
                  }),
                  (o.prototype._tick = function () {
                    if (this.isPaused || this.isFinished) return !1;
                    var e = null,
                      t = Math.min(this.max, this.index + 16384);
                    if (this.index >= this.max) return this.end();
                    switch (this.type) {
                      case "string":
                        e = this.data.substring(this.index, t);
                        break;
                      case "uint8array":
                        e = this.data.subarray(this.index, t);
                        break;
                      case "array":
                      case "nodebuffer":
                        e = this.data.slice(this.index, t);
                    }
                    return (
                      (this.index = t),
                      this.push({
                        data: e,
                        meta: {
                          percent: this.max ? (this.index / this.max) * 100 : 0,
                        },
                      })
                    );
                  }),
                  (t.exports = o));
              },
              { "../utils": 32, "./GenericWorker": 28 },
            ],
            28: [
              function (e, t, r) {
                "use strict";
                function n(e) {
                  ((this.name = e || "default"),
                    (this.streamInfo = {}),
                    (this.generatedError = null),
                    (this.extraStreamInfo = {}),
                    (this.isPaused = !0),
                    (this.isFinished = !1),
                    (this.isLocked = !1),
                    (this._listeners = { data: [], end: [], error: [] }),
                    (this.previous = null));
                }
                ((n.prototype = {
                  push: function (e) {
                    this.emit("data", e);
                  },
                  end: function () {
                    if (this.isFinished) return !1;
                    this.flush();
                    try {
                      (this.emit("end"),
                        this.cleanUp(),
                        (this.isFinished = !0));
                    } catch (e) {
                      this.emit("error", e);
                    }
                    return !0;
                  },
                  error: function (e) {
                    return (
                      !this.isFinished &&
                      (this.isPaused
                        ? (this.generatedError = e)
                        : ((this.isFinished = !0),
                          this.emit("error", e),
                          this.previous && this.previous.error(e),
                          this.cleanUp()),
                      !0)
                    );
                  },
                  on: function (e, t) {
                    return (this._listeners[e].push(t), this);
                  },
                  cleanUp: function () {
                    ((this.streamInfo =
                      this.generatedError =
                      this.extraStreamInfo =
                        null),
                      (this._listeners = []));
                  },
                  emit: function (e, t) {
                    if (this._listeners[e])
                      for (var r = 0; r < this._listeners[e].length; r++)
                        this._listeners[e][r].call(this, t);
                  },
                  pipe: function (e) {
                    return e.registerPrevious(this);
                  },
                  registerPrevious: function (e) {
                    if (this.isLocked)
                      throw new Error(
                        "The stream '" + this + "' has already been used.",
                      );
                    ((this.streamInfo = e.streamInfo),
                      this.mergeStreamInfo(),
                      (this.previous = e));
                    var t = this;
                    return (
                      e.on("data", function (e) {
                        t.processChunk(e);
                      }),
                      e.on("end", function () {
                        t.end();
                      }),
                      e.on("error", function (e) {
                        t.error(e);
                      }),
                      this
                    );
                  },
                  pause: function () {
                    return (
                      !this.isPaused &&
                      !this.isFinished &&
                      ((this.isPaused = !0),
                      this.previous && this.previous.pause(),
                      !0)
                    );
                  },
                  resume: function () {
                    if (!this.isPaused || this.isFinished) return !1;
                    var e = (this.isPaused = !1);
                    return (
                      this.generatedError &&
                        (this.error(this.generatedError), (e = !0)),
                      this.previous && this.previous.resume(),
                      !e
                    );
                  },
                  flush: function () {},
                  processChunk: function (e) {
                    this.push(e);
                  },
                  withStreamInfo: function (e, t) {
                    return (
                      (this.extraStreamInfo[e] = t),
                      this.mergeStreamInfo(),
                      this
                    );
                  },
                  mergeStreamInfo: function () {
                    for (var e in this.extraStreamInfo)
                      Object.prototype.hasOwnProperty.call(
                        this.extraStreamInfo,
                        e,
                      ) && (this.streamInfo[e] = this.extraStreamInfo[e]);
                  },
                  lock: function () {
                    if (this.isLocked)
                      throw new Error(
                        "The stream '" + this + "' has already been used.",
                      );
                    ((this.isLocked = !0),
                      this.previous && this.previous.lock());
                  },
                  toString: function () {
                    var e = "Worker " + this.name;
                    return this.previous ? this.previous + " -> " + e : e;
                  },
                }),
                  (t.exports = n));
              },
              {},
            ],
            29: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils"),
                  i = e("./ConvertWorker"),
                  o = e("./GenericWorker"),
                  s = e("../base64"),
                  a = e("../support"),
                  d = e("../external"),
                  l = null;
                if (a.nodestream)
                  try {
                    l = e("../nodejs/NodejsStreamOutputAdapter");
                  } catch (e) {}
                function c(e, t) {
                  return new d.Promise(function (r, i) {
                    var o = [],
                      a = e._internalType,
                      d = e._outputType,
                      l = e._mimeType;
                    e.on("data", function (e, r) {
                      (o.push(e), t && t(r));
                    })
                      .on("error", function (e) {
                        ((o = []), i(e));
                      })
                      .on("end", function () {
                        try {
                          var e = (function (e, t, r) {
                            switch (e) {
                              case "blob":
                                return n.newBlob(
                                  n.transformTo("arraybuffer", t),
                                  r,
                                );
                              case "base64":
                                return s.encode(t);
                              default:
                                return n.transformTo(e, t);
                            }
                          })(
                            d,
                            (function (e, t) {
                              var r,
                                n = 0,
                                i = null,
                                o = 0;
                              for (r = 0; r < t.length; r++) o += t[r].length;
                              switch (e) {
                                case "string":
                                  return t.join("");
                                case "array":
                                  return Array.prototype.concat.apply([], t);
                                case "uint8array":
                                  for (
                                    i = new Uint8Array(o), r = 0;
                                    r < t.length;
                                    r++
                                  )
                                    (i.set(t[r], n), (n += t[r].length));
                                  return i;
                                case "nodebuffer":
                                  return Buffer.concat(t);
                                default:
                                  throw new Error(
                                    "concat : unsupported type '" + e + "'",
                                  );
                              }
                            })(a, o),
                            l,
                          );
                          r(e);
                        } catch (e) {
                          i(e);
                        }
                        o = [];
                      })
                      .resume();
                  });
                }
                function u(e, t, r) {
                  var s = t;
                  switch (t) {
                    case "blob":
                    case "arraybuffer":
                      s = "uint8array";
                      break;
                    case "base64":
                      s = "string";
                  }
                  try {
                    ((this._internalType = s),
                      (this._outputType = t),
                      (this._mimeType = r),
                      n.checkSupport(s),
                      (this._worker = e.pipe(new i(s))),
                      e.lock());
                  } catch (e) {
                    ((this._worker = new o("error")), this._worker.error(e));
                  }
                }
                ((u.prototype = {
                  accumulate: function (e) {
                    return c(this, e);
                  },
                  on: function (e, t) {
                    var r = this;
                    return (
                      "data" === e
                        ? this._worker.on(e, function (e) {
                            t.call(r, e.data, e.meta);
                          })
                        : this._worker.on(e, function () {
                            n.delay(t, arguments, r);
                          }),
                      this
                    );
                  },
                  resume: function () {
                    return (
                      n.delay(this._worker.resume, [], this._worker),
                      this
                    );
                  },
                  pause: function () {
                    return (this._worker.pause(), this);
                  },
                  toNodejsStream: function (e) {
                    if (
                      (n.checkSupport("nodestream"),
                      "nodebuffer" !== this._outputType)
                    )
                      throw new Error(
                        this._outputType + " is not supported by this method",
                      );
                    return new l(
                      this,
                      { objectMode: "nodebuffer" !== this._outputType },
                      e,
                    );
                  },
                }),
                  (t.exports = u));
              },
              {
                "../base64": 1,
                "../external": 6,
                "../nodejs/NodejsStreamOutputAdapter": 13,
                "../support": 30,
                "../utils": 32,
                "./ConvertWorker": 24,
                "./GenericWorker": 28,
              },
            ],
            30: [
              function (e, t, r) {
                "use strict";
                if (
                  ((r.base64 = !0),
                  (r.array = !0),
                  (r.string = !0),
                  (r.arraybuffer =
                    "undefined" != typeof ArrayBuffer &&
                    "undefined" != typeof Uint8Array),
                  (r.nodebuffer = "undefined" != typeof Buffer),
                  (r.uint8array = "undefined" != typeof Uint8Array),
                  "undefined" == typeof ArrayBuffer)
                )
                  r.blob = !1;
                else {
                  var n = new ArrayBuffer(0);
                  try {
                    r.blob =
                      0 === new Blob([n], { type: "application/zip" }).size;
                  } catch (e) {
                    try {
                      var i = new (self.BlobBuilder ||
                        self.WebKitBlobBuilder ||
                        self.MozBlobBuilder ||
                        self.MSBlobBuilder)();
                      (i.append(n),
                        (r.blob = 0 === i.getBlob("application/zip").size));
                    } catch (e) {
                      r.blob = !1;
                    }
                  }
                }
                try {
                  r.nodestream = !!e("readable-stream").Readable;
                } catch (e) {
                  r.nodestream = !1;
                }
              },
              { "readable-stream": 16 },
            ],
            31: [
              function (e, t, r) {
                "use strict";
                for (
                  var n = e("./utils"),
                    i = e("./support"),
                    o = e("./nodejsUtils"),
                    s = e("./stream/GenericWorker"),
                    a = new Array(256),
                    d = 0;
                  d < 256;
                  d++
                )
                  a[d] =
                    252 <= d
                      ? 6
                      : 248 <= d
                        ? 5
                        : 240 <= d
                          ? 4
                          : 224 <= d
                            ? 3
                            : 192 <= d
                              ? 2
                              : 1;
                function l() {
                  (s.call(this, "utf-8 decode"), (this.leftOver = null));
                }
                function c() {
                  s.call(this, "utf-8 encode");
                }
                ((a[254] = a[254] = 1),
                  (r.utf8encode = function (e) {
                    return i.nodebuffer
                      ? o.newBufferFrom(e, "utf-8")
                      : (function (e) {
                          var t,
                            r,
                            n,
                            o,
                            s,
                            a = e.length,
                            d = 0;
                          for (o = 0; o < a; o++)
                            (55296 == (64512 & (r = e.charCodeAt(o))) &&
                              o + 1 < a &&
                              56320 == (64512 & (n = e.charCodeAt(o + 1))) &&
                              ((r = 65536 + ((r - 55296) << 10) + (n - 56320)),
                              o++),
                              (d +=
                                r < 128
                                  ? 1
                                  : r < 2048
                                    ? 2
                                    : r < 65536
                                      ? 3
                                      : 4));
                          for (
                            t = i.uint8array ? new Uint8Array(d) : new Array(d),
                              o = s = 0;
                            s < d;
                            o++
                          )
                            (55296 == (64512 & (r = e.charCodeAt(o))) &&
                              o + 1 < a &&
                              56320 == (64512 & (n = e.charCodeAt(o + 1))) &&
                              ((r = 65536 + ((r - 55296) << 10) + (n - 56320)),
                              o++),
                              r < 128
                                ? (t[s++] = r)
                                : (r < 2048
                                    ? (t[s++] = 192 | (r >>> 6))
                                    : (r < 65536
                                        ? (t[s++] = 224 | (r >>> 12))
                                        : ((t[s++] = 240 | (r >>> 18)),
                                          (t[s++] = 128 | ((r >>> 12) & 63))),
                                      (t[s++] = 128 | ((r >>> 6) & 63))),
                                  (t[s++] = 128 | (63 & r))));
                          return t;
                        })(e);
                  }),
                  (r.utf8decode = function (e) {
                    return i.nodebuffer
                      ? n.transformTo("nodebuffer", e).toString("utf-8")
                      : (function (e) {
                          var t,
                            r,
                            i,
                            o,
                            s = e.length,
                            d = new Array(2 * s);
                          for (t = r = 0; t < s; )
                            if ((i = e[t++]) < 128) d[r++] = i;
                            else if (4 < (o = a[i]))
                              ((d[r++] = 65533), (t += o - 1));
                            else {
                              for (
                                i &= 2 === o ? 31 : 3 === o ? 15 : 7;
                                1 < o && t < s;

                              )
                                ((i = (i << 6) | (63 & e[t++])), o--);
                              1 < o
                                ? (d[r++] = 65533)
                                : i < 65536
                                  ? (d[r++] = i)
                                  : ((i -= 65536),
                                    (d[r++] = 55296 | ((i >> 10) & 1023)),
                                    (d[r++] = 56320 | (1023 & i)));
                            }
                          return (
                            d.length !== r &&
                              (d.subarray
                                ? (d = d.subarray(0, r))
                                : (d.length = r)),
                            n.applyFromCharCode(d)
                          );
                        })(
                          (e = n.transformTo(
                            i.uint8array ? "uint8array" : "array",
                            e,
                          )),
                        );
                  }),
                  n.inherits(l, s),
                  (l.prototype.processChunk = function (e) {
                    var t = n.transformTo(
                      i.uint8array ? "uint8array" : "array",
                      e.data,
                    );
                    if (this.leftOver && this.leftOver.length) {
                      if (i.uint8array) {
                        var o = t;
                        ((t = new Uint8Array(
                          o.length + this.leftOver.length,
                        )).set(this.leftOver, 0),
                          t.set(o, this.leftOver.length));
                      } else t = this.leftOver.concat(t);
                      this.leftOver = null;
                    }
                    var s = (function (e, t) {
                        var r;
                        for (
                          (t = t || e.length) > e.length && (t = e.length),
                            r = t - 1;
                          0 <= r && 128 == (192 & e[r]);

                        )
                          r--;
                        return r < 0 || 0 === r ? t : r + a[e[r]] > t ? r : t;
                      })(t),
                      d = t;
                    (s !== t.length &&
                      (i.uint8array
                        ? ((d = t.subarray(0, s)),
                          (this.leftOver = t.subarray(s, t.length)))
                        : ((d = t.slice(0, s)),
                          (this.leftOver = t.slice(s, t.length)))),
                      this.push({ data: r.utf8decode(d), meta: e.meta }));
                  }),
                  (l.prototype.flush = function () {
                    this.leftOver &&
                      this.leftOver.length &&
                      (this.push({
                        data: r.utf8decode(this.leftOver),
                        meta: {},
                      }),
                      (this.leftOver = null));
                  }),
                  (r.Utf8DecodeWorker = l),
                  n.inherits(c, s),
                  (c.prototype.processChunk = function (e) {
                    this.push({ data: r.utf8encode(e.data), meta: e.meta });
                  }),
                  (r.Utf8EncodeWorker = c));
              },
              {
                "./nodejsUtils": 14,
                "./stream/GenericWorker": 28,
                "./support": 30,
                "./utils": 32,
              },
            ],
            32: [
              function (e, t, r) {
                "use strict";
                var n = e("./support"),
                  i = e("./base64"),
                  o = e("./nodejsUtils"),
                  s = e("./external");
                function a(e) {
                  return e;
                }
                function d(e, t) {
                  for (var r = 0; r < e.length; ++r)
                    t[r] = 255 & e.charCodeAt(r);
                  return t;
                }
                (e("setimmediate"),
                  (r.newBlob = function (e, t) {
                    r.checkSupport("blob");
                    try {
                      return new Blob([e], { type: t });
                    } catch (r) {
                      try {
                        var n = new (self.BlobBuilder ||
                          self.WebKitBlobBuilder ||
                          self.MozBlobBuilder ||
                          self.MSBlobBuilder)();
                        return (n.append(e), n.getBlob(t));
                      } catch (e) {
                        throw new Error("Bug : can't construct the Blob.");
                      }
                    }
                  }));
                var l = {
                  stringifyByChunk: function (e, t, r) {
                    var n = [],
                      i = 0,
                      o = e.length;
                    if (o <= r) return String.fromCharCode.apply(null, e);
                    for (; i < o; )
                      ("array" === t || "nodebuffer" === t
                        ? n.push(
                            String.fromCharCode.apply(
                              null,
                              e.slice(i, Math.min(i + r, o)),
                            ),
                          )
                        : n.push(
                            String.fromCharCode.apply(
                              null,
                              e.subarray(i, Math.min(i + r, o)),
                            ),
                          ),
                        (i += r));
                    return n.join("");
                  },
                  stringifyByChar: function (e) {
                    for (var t = "", r = 0; r < e.length; r++)
                      t += String.fromCharCode(e[r]);
                    return t;
                  },
                  applyCanBeUsed: {
                    uint8array: (function () {
                      try {
                        return (
                          n.uint8array &&
                          1 ===
                            String.fromCharCode.apply(null, new Uint8Array(1))
                              .length
                        );
                      } catch (e) {
                        return !1;
                      }
                    })(),
                    nodebuffer: (function () {
                      try {
                        return (
                          n.nodebuffer &&
                          1 ===
                            String.fromCharCode.apply(null, o.allocBuffer(1))
                              .length
                        );
                      } catch (e) {
                        return !1;
                      }
                    })(),
                  },
                };
                function c(e) {
                  var t = 65536,
                    n = r.getTypeOf(e),
                    i = !0;
                  if (
                    ("uint8array" === n
                      ? (i = l.applyCanBeUsed.uint8array)
                      : "nodebuffer" === n && (i = l.applyCanBeUsed.nodebuffer),
                    i)
                  )
                    for (; 1 < t; )
                      try {
                        return l.stringifyByChunk(e, n, t);
                      } catch (e) {
                        t = Math.floor(t / 2);
                      }
                  return l.stringifyByChar(e);
                }
                function u(e, t) {
                  for (var r = 0; r < e.length; r++) t[r] = e[r];
                  return t;
                }
                r.applyFromCharCode = c;
                var f = {};
                ((f.string = {
                  string: a,
                  array: function (e) {
                    return d(e, new Array(e.length));
                  },
                  arraybuffer: function (e) {
                    return f.string.uint8array(e).buffer;
                  },
                  uint8array: function (e) {
                    return d(e, new Uint8Array(e.length));
                  },
                  nodebuffer: function (e) {
                    return d(e, o.allocBuffer(e.length));
                  },
                }),
                  (f.array = {
                    string: c,
                    array: a,
                    arraybuffer: function (e) {
                      return new Uint8Array(e).buffer;
                    },
                    uint8array: function (e) {
                      return new Uint8Array(e);
                    },
                    nodebuffer: function (e) {
                      return o.newBufferFrom(e);
                    },
                  }),
                  (f.arraybuffer = {
                    string: function (e) {
                      return c(new Uint8Array(e));
                    },
                    array: function (e) {
                      return u(new Uint8Array(e), new Array(e.byteLength));
                    },
                    arraybuffer: a,
                    uint8array: function (e) {
                      return new Uint8Array(e);
                    },
                    nodebuffer: function (e) {
                      return o.newBufferFrom(new Uint8Array(e));
                    },
                  }),
                  (f.uint8array = {
                    string: c,
                    array: function (e) {
                      return u(e, new Array(e.length));
                    },
                    arraybuffer: function (e) {
                      return e.buffer;
                    },
                    uint8array: a,
                    nodebuffer: function (e) {
                      return o.newBufferFrom(e);
                    },
                  }),
                  (f.nodebuffer = {
                    string: c,
                    array: function (e) {
                      return u(e, new Array(e.length));
                    },
                    arraybuffer: function (e) {
                      return f.nodebuffer.uint8array(e).buffer;
                    },
                    uint8array: function (e) {
                      return u(e, new Uint8Array(e.length));
                    },
                    nodebuffer: a,
                  }),
                  (r.transformTo = function (e, t) {
                    if (((t = t || ""), !e)) return t;
                    r.checkSupport(e);
                    var n = r.getTypeOf(t);
                    return f[n][e](t);
                  }),
                  (r.resolve = function (e) {
                    for (
                      var t = e.split("/"), r = [], n = 0;
                      n < t.length;
                      n++
                    ) {
                      var i = t[n];
                      "." === i ||
                        ("" === i && 0 !== n && n !== t.length - 1) ||
                        (".." === i ? r.pop() : r.push(i));
                    }
                    return r.join("/");
                  }),
                  (r.getTypeOf = function (e) {
                    return "string" == typeof e
                      ? "string"
                      : "[object Array]" === Object.prototype.toString.call(e)
                        ? "array"
                        : n.nodebuffer && o.isBuffer(e)
                          ? "nodebuffer"
                          : n.uint8array && e instanceof Uint8Array
                            ? "uint8array"
                            : n.arraybuffer && e instanceof ArrayBuffer
                              ? "arraybuffer"
                              : void 0;
                  }),
                  (r.checkSupport = function (e) {
                    if (!n[e.toLowerCase()])
                      throw new Error(e + " is not supported by this platform");
                  }),
                  (r.MAX_VALUE_16BITS = 65535),
                  (r.MAX_VALUE_32BITS = -1),
                  (r.pretty = function (e) {
                    var t,
                      r,
                      n = "";
                    for (r = 0; r < (e || "").length; r++)
                      n +=
                        "\\x" +
                        ((t = e.charCodeAt(r)) < 16 ? "0" : "") +
                        t.toString(16).toUpperCase();
                    return n;
                  }),
                  (r.delay = function (e, t, r) {
                    setImmediate(function () {
                      e.apply(r || null, t || []);
                    });
                  }),
                  (r.inherits = function (e, t) {
                    function r() {}
                    ((r.prototype = t.prototype), (e.prototype = new r()));
                  }),
                  (r.extend = function () {
                    var e,
                      t,
                      r = {};
                    for (e = 0; e < arguments.length; e++)
                      for (t in arguments[e])
                        Object.prototype.hasOwnProperty.call(arguments[e], t) &&
                          void 0 === r[t] &&
                          (r[t] = arguments[e][t]);
                    return r;
                  }),
                  (r.prepareContent = function (e, t, o, a, l) {
                    return s.Promise.resolve(t)
                      .then(function (e) {
                        return n.blob &&
                          (e instanceof Blob ||
                            -1 !==
                              ["[object File]", "[object Blob]"].indexOf(
                                Object.prototype.toString.call(e),
                              )) &&
                          "undefined" != typeof FileReader
                          ? new s.Promise(function (t, r) {
                              var n = new FileReader();
                              ((n.onload = function (e) {
                                t(e.target.result);
                              }),
                                (n.onerror = function (e) {
                                  r(e.target.error);
                                }),
                                n.readAsArrayBuffer(e));
                            })
                          : e;
                      })
                      .then(function (t) {
                        var c = r.getTypeOf(t);
                        return c
                          ? ("arraybuffer" === c
                              ? (t = r.transformTo("uint8array", t))
                              : "string" === c &&
                                (l
                                  ? (t = i.decode(t))
                                  : o &&
                                    !0 !== a &&
                                    (t = (function (e) {
                                      return d(
                                        e,
                                        n.uint8array
                                          ? new Uint8Array(e.length)
                                          : new Array(e.length),
                                      );
                                    })(t))),
                            t)
                          : s.Promise.reject(
                              new Error(
                                "Can't read the data of '" +
                                  e +
                                  "'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?",
                              ),
                            );
                      });
                  }));
              },
              {
                "./base64": 1,
                "./external": 6,
                "./nodejsUtils": 14,
                "./support": 30,
                setimmediate: 54,
              },
            ],
            33: [
              function (e, t, r) {
                "use strict";
                var n = e("./reader/readerFor"),
                  i = e("./utils"),
                  o = e("./signature"),
                  s = e("./zipEntry"),
                  a = e("./support");
                function d(e) {
                  ((this.files = []), (this.loadOptions = e));
                }
                ((d.prototype = {
                  checkSignature: function (e) {
                    if (!this.reader.readAndCheckSignature(e)) {
                      this.reader.index -= 4;
                      var t = this.reader.readString(4);
                      throw new Error(
                        "Corrupted zip or bug: unexpected signature (" +
                          i.pretty(t) +
                          ", expected " +
                          i.pretty(e) +
                          ")",
                      );
                    }
                  },
                  isSignature: function (e, t) {
                    var r = this.reader.index;
                    this.reader.setIndex(e);
                    var n = this.reader.readString(4) === t;
                    return (this.reader.setIndex(r), n);
                  },
                  readBlockEndOfCentral: function () {
                    ((this.diskNumber = this.reader.readInt(2)),
                      (this.diskWithCentralDirStart = this.reader.readInt(2)),
                      (this.centralDirRecordsOnThisDisk =
                        this.reader.readInt(2)),
                      (this.centralDirRecords = this.reader.readInt(2)),
                      (this.centralDirSize = this.reader.readInt(4)),
                      (this.centralDirOffset = this.reader.readInt(4)),
                      (this.zipCommentLength = this.reader.readInt(2)));
                    var e = this.reader.readData(this.zipCommentLength),
                      t = a.uint8array ? "uint8array" : "array",
                      r = i.transformTo(t, e);
                    this.zipComment = this.loadOptions.decodeFileName(r);
                  },
                  readBlockZip64EndOfCentral: function () {
                    ((this.zip64EndOfCentralSize = this.reader.readInt(8)),
                      this.reader.skip(4),
                      (this.diskNumber = this.reader.readInt(4)),
                      (this.diskWithCentralDirStart = this.reader.readInt(4)),
                      (this.centralDirRecordsOnThisDisk =
                        this.reader.readInt(8)),
                      (this.centralDirRecords = this.reader.readInt(8)),
                      (this.centralDirSize = this.reader.readInt(8)),
                      (this.centralDirOffset = this.reader.readInt(8)),
                      (this.zip64ExtensibleData = {}));
                    for (
                      var e, t, r, n = this.zip64EndOfCentralSize - 44;
                      0 < n;

                    )
                      ((e = this.reader.readInt(2)),
                        (t = this.reader.readInt(4)),
                        (r = this.reader.readData(t)),
                        (this.zip64ExtensibleData[e] = {
                          id: e,
                          length: t,
                          value: r,
                        }));
                  },
                  readBlockZip64EndOfCentralLocator: function () {
                    if (
                      ((this.diskWithZip64CentralDirStart =
                        this.reader.readInt(4)),
                      (this.relativeOffsetEndOfZip64CentralDir =
                        this.reader.readInt(8)),
                      (this.disksCount = this.reader.readInt(4)),
                      1 < this.disksCount)
                    )
                      throw new Error("Multi-volumes zip are not supported");
                  },
                  readLocalFiles: function () {
                    var e, t;
                    for (e = 0; e < this.files.length; e++)
                      ((t = this.files[e]),
                        this.reader.setIndex(t.localHeaderOffset),
                        this.checkSignature(o.LOCAL_FILE_HEADER),
                        t.readLocalPart(this.reader),
                        t.handleUTF8(),
                        t.processAttributes());
                  },
                  readCentralDir: function () {
                    var e;
                    for (
                      this.reader.setIndex(this.centralDirOffset);
                      this.reader.readAndCheckSignature(o.CENTRAL_FILE_HEADER);

                    )
                      ((e = new s(
                        { zip64: this.zip64 },
                        this.loadOptions,
                      )).readCentralPart(this.reader),
                        this.files.push(e));
                    if (
                      this.centralDirRecords !== this.files.length &&
                      0 !== this.centralDirRecords &&
                      0 === this.files.length
                    )
                      throw new Error(
                        "Corrupted zip or bug: expected " +
                          this.centralDirRecords +
                          " records in central dir, got " +
                          this.files.length,
                      );
                  },
                  readEndOfCentral: function () {
                    var e = this.reader.lastIndexOfSignature(
                      o.CENTRAL_DIRECTORY_END,
                    );
                    if (e < 0)
                      throw this.isSignature(0, o.LOCAL_FILE_HEADER)
                        ? new Error(
                            "Corrupted zip: can't find end of central directory",
                          )
                        : new Error(
                            "Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html",
                          );
                    this.reader.setIndex(e);
                    var t = e;
                    if (
                      (this.checkSignature(o.CENTRAL_DIRECTORY_END),
                      this.readBlockEndOfCentral(),
                      this.diskNumber === i.MAX_VALUE_16BITS ||
                        this.diskWithCentralDirStart === i.MAX_VALUE_16BITS ||
                        this.centralDirRecordsOnThisDisk ===
                          i.MAX_VALUE_16BITS ||
                        this.centralDirRecords === i.MAX_VALUE_16BITS ||
                        this.centralDirSize === i.MAX_VALUE_32BITS ||
                        this.centralDirOffset === i.MAX_VALUE_32BITS)
                    ) {
                      if (
                        ((this.zip64 = !0),
                        (e = this.reader.lastIndexOfSignature(
                          o.ZIP64_CENTRAL_DIRECTORY_LOCATOR,
                        )) < 0)
                      )
                        throw new Error(
                          "Corrupted zip: can't find the ZIP64 end of central directory locator",
                        );
                      if (
                        (this.reader.setIndex(e),
                        this.checkSignature(o.ZIP64_CENTRAL_DIRECTORY_LOCATOR),
                        this.readBlockZip64EndOfCentralLocator(),
                        !this.isSignature(
                          this.relativeOffsetEndOfZip64CentralDir,
                          o.ZIP64_CENTRAL_DIRECTORY_END,
                        ) &&
                          ((this.relativeOffsetEndOfZip64CentralDir =
                            this.reader.lastIndexOfSignature(
                              o.ZIP64_CENTRAL_DIRECTORY_END,
                            )),
                          this.relativeOffsetEndOfZip64CentralDir < 0))
                      )
                        throw new Error(
                          "Corrupted zip: can't find the ZIP64 end of central directory",
                        );
                      (this.reader.setIndex(
                        this.relativeOffsetEndOfZip64CentralDir,
                      ),
                        this.checkSignature(o.ZIP64_CENTRAL_DIRECTORY_END),
                        this.readBlockZip64EndOfCentral());
                    }
                    var r = this.centralDirOffset + this.centralDirSize;
                    this.zip64 &&
                      ((r += 20), (r += 12 + this.zip64EndOfCentralSize));
                    var n = t - r;
                    if (0 < n)
                      this.isSignature(t, o.CENTRAL_FILE_HEADER) ||
                        (this.reader.zero = n);
                    else if (n < 0)
                      throw new Error(
                        "Corrupted zip: missing " + Math.abs(n) + " bytes.",
                      );
                  },
                  prepareReader: function (e) {
                    this.reader = n(e);
                  },
                  load: function (e) {
                    (this.prepareReader(e),
                      this.readEndOfCentral(),
                      this.readCentralDir(),
                      this.readLocalFiles());
                  },
                }),
                  (t.exports = d));
              },
              {
                "./reader/readerFor": 22,
                "./signature": 23,
                "./support": 30,
                "./utils": 32,
                "./zipEntry": 34,
              },
            ],
            34: [
              function (e, t, r) {
                "use strict";
                var n = e("./reader/readerFor"),
                  i = e("./utils"),
                  o = e("./compressedObject"),
                  s = e("./crc32"),
                  a = e("./utf8"),
                  d = e("./compressions"),
                  l = e("./support");
                function c(e, t) {
                  ((this.options = e), (this.loadOptions = t));
                }
                ((c.prototype = {
                  isEncrypted: function () {
                    return !(1 & ~this.bitFlag);
                  },
                  useUTF8: function () {
                    return !(2048 & ~this.bitFlag);
                  },
                  readLocalPart: function (e) {
                    var t, r;
                    if (
                      (e.skip(22),
                      (this.fileNameLength = e.readInt(2)),
                      (r = e.readInt(2)),
                      (this.fileName = e.readData(this.fileNameLength)),
                      e.skip(r),
                      -1 === this.compressedSize ||
                        -1 === this.uncompressedSize)
                    )
                      throw new Error(
                        "Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)",
                      );
                    if (
                      null ===
                      (t = (function (e) {
                        for (var t in d)
                          if (
                            Object.prototype.hasOwnProperty.call(d, t) &&
                            d[t].magic === e
                          )
                            return d[t];
                        return null;
                      })(this.compressionMethod))
                    )
                      throw new Error(
                        "Corrupted zip : compression " +
                          i.pretty(this.compressionMethod) +
                          " unknown (inner file : " +
                          i.transformTo("string", this.fileName) +
                          ")",
                      );
                    this.decompressed = new o(
                      this.compressedSize,
                      this.uncompressedSize,
                      this.crc32,
                      t,
                      e.readData(this.compressedSize),
                    );
                  },
                  readCentralPart: function (e) {
                    ((this.versionMadeBy = e.readInt(2)),
                      e.skip(2),
                      (this.bitFlag = e.readInt(2)),
                      (this.compressionMethod = e.readString(2)),
                      (this.date = e.readDate()),
                      (this.crc32 = e.readInt(4)),
                      (this.compressedSize = e.readInt(4)),
                      (this.uncompressedSize = e.readInt(4)));
                    var t = e.readInt(2);
                    if (
                      ((this.extraFieldsLength = e.readInt(2)),
                      (this.fileCommentLength = e.readInt(2)),
                      (this.diskNumberStart = e.readInt(2)),
                      (this.internalFileAttributes = e.readInt(2)),
                      (this.externalFileAttributes = e.readInt(4)),
                      (this.localHeaderOffset = e.readInt(4)),
                      this.isEncrypted())
                    )
                      throw new Error("Encrypted zip are not supported");
                    (e.skip(t),
                      this.readExtraFields(e),
                      this.parseZIP64ExtraField(e),
                      (this.fileComment = e.readData(this.fileCommentLength)));
                  },
                  processAttributes: function () {
                    ((this.unixPermissions = null),
                      (this.dosPermissions = null));
                    var e = this.versionMadeBy >> 8;
                    ((this.dir = !!(16 & this.externalFileAttributes)),
                      0 == e &&
                        (this.dosPermissions =
                          63 & this.externalFileAttributes),
                      3 == e &&
                        (this.unixPermissions =
                          (this.externalFileAttributes >> 16) & 65535),
                      this.dir ||
                        "/" !== this.fileNameStr.slice(-1) ||
                        (this.dir = !0));
                  },
                  parseZIP64ExtraField: function () {
                    if (this.extraFields[1]) {
                      var e = n(this.extraFields[1].value);
                      (this.uncompressedSize === i.MAX_VALUE_32BITS &&
                        (this.uncompressedSize = e.readInt(8)),
                        this.compressedSize === i.MAX_VALUE_32BITS &&
                          (this.compressedSize = e.readInt(8)),
                        this.localHeaderOffset === i.MAX_VALUE_32BITS &&
                          (this.localHeaderOffset = e.readInt(8)),
                        this.diskNumberStart === i.MAX_VALUE_32BITS &&
                          (this.diskNumberStart = e.readInt(4)));
                    }
                  },
                  readExtraFields: function (e) {
                    var t,
                      r,
                      n,
                      i = e.index + this.extraFieldsLength;
                    for (
                      this.extraFields || (this.extraFields = {});
                      e.index + 4 < i;

                    )
                      ((t = e.readInt(2)),
                        (r = e.readInt(2)),
                        (n = e.readData(r)),
                        (this.extraFields[t] = { id: t, length: r, value: n }));
                    e.setIndex(i);
                  },
                  handleUTF8: function () {
                    var e = l.uint8array ? "uint8array" : "array";
                    if (this.useUTF8())
                      ((this.fileNameStr = a.utf8decode(this.fileName)),
                        (this.fileCommentStr = a.utf8decode(this.fileComment)));
                    else {
                      var t = this.findExtraFieldUnicodePath();
                      if (null !== t) this.fileNameStr = t;
                      else {
                        var r = i.transformTo(e, this.fileName);
                        this.fileNameStr = this.loadOptions.decodeFileName(r);
                      }
                      var n = this.findExtraFieldUnicodeComment();
                      if (null !== n) this.fileCommentStr = n;
                      else {
                        var o = i.transformTo(e, this.fileComment);
                        this.fileCommentStr =
                          this.loadOptions.decodeFileName(o);
                      }
                    }
                  },
                  findExtraFieldUnicodePath: function () {
                    var e = this.extraFields[28789];
                    if (e) {
                      var t = n(e.value);
                      return 1 !== t.readInt(1) ||
                        s(this.fileName) !== t.readInt(4)
                        ? null
                        : a.utf8decode(t.readData(e.length - 5));
                    }
                    return null;
                  },
                  findExtraFieldUnicodeComment: function () {
                    var e = this.extraFields[25461];
                    if (e) {
                      var t = n(e.value);
                      return 1 !== t.readInt(1) ||
                        s(this.fileComment) !== t.readInt(4)
                        ? null
                        : a.utf8decode(t.readData(e.length - 5));
                    }
                    return null;
                  },
                }),
                  (t.exports = c));
              },
              {
                "./compressedObject": 2,
                "./compressions": 3,
                "./crc32": 4,
                "./reader/readerFor": 22,
                "./support": 30,
                "./utf8": 31,
                "./utils": 32,
              },
            ],
            35: [
              function (e, t, r) {
                "use strict";
                function n(e, t, r) {
                  ((this.name = e),
                    (this.dir = r.dir),
                    (this.date = r.date),
                    (this.comment = r.comment),
                    (this.unixPermissions = r.unixPermissions),
                    (this.dosPermissions = r.dosPermissions),
                    (this._data = t),
                    (this._dataBinary = r.binary),
                    (this.options = {
                      compression: r.compression,
                      compressionOptions: r.compressionOptions,
                    }));
                }
                var i = e("./stream/StreamHelper"),
                  o = e("./stream/DataWorker"),
                  s = e("./utf8"),
                  a = e("./compressedObject"),
                  d = e("./stream/GenericWorker");
                n.prototype = {
                  internalStream: function (e) {
                    var t = null,
                      r = "string";
                    try {
                      if (!e) throw new Error("No output type specified.");
                      var n =
                        "string" === (r = e.toLowerCase()) || "text" === r;
                      (("binarystring" !== r && "text" !== r) || (r = "string"),
                        (t = this._decompressWorker()));
                      var o = !this._dataBinary;
                      (o && !n && (t = t.pipe(new s.Utf8EncodeWorker())),
                        !o && n && (t = t.pipe(new s.Utf8DecodeWorker())));
                    } catch (e) {
                      (t = new d("error")).error(e);
                    }
                    return new i(t, r, "");
                  },
                  async: function (e, t) {
                    return this.internalStream(e).accumulate(t);
                  },
                  nodeStream: function (e, t) {
                    return this.internalStream(
                      e || "nodebuffer",
                    ).toNodejsStream(t);
                  },
                  _compressWorker: function (e, t) {
                    if (
                      this._data instanceof a &&
                      this._data.compression.magic === e.magic
                    )
                      return this._data.getCompressedWorker();
                    var r = this._decompressWorker();
                    return (
                      this._dataBinary ||
                        (r = r.pipe(new s.Utf8EncodeWorker())),
                      a.createWorkerFrom(r, e, t)
                    );
                  },
                  _decompressWorker: function () {
                    return this._data instanceof a
                      ? this._data.getContentWorker()
                      : this._data instanceof d
                        ? this._data
                        : new o(this._data);
                  },
                };
                for (
                  var l = [
                      "asText",
                      "asBinary",
                      "asNodeBuffer",
                      "asUint8Array",
                      "asArrayBuffer",
                    ],
                    c = function () {
                      throw new Error(
                        "This method has been removed in JSZip 3.0, please check the upgrade guide.",
                      );
                    },
                    u = 0;
                  u < l.length;
                  u++
                )
                  n.prototype[l[u]] = c;
                t.exports = n;
              },
              {
                "./compressedObject": 2,
                "./stream/DataWorker": 27,
                "./stream/GenericWorker": 28,
                "./stream/StreamHelper": 29,
                "./utf8": 31,
              },
            ],
            36: [
              function (e, t, r) {
                (function (e) {
                  "use strict";
                  var r,
                    n,
                    i = e.MutationObserver || e.WebKitMutationObserver;
                  if (i) {
                    var o = 0,
                      s = new i(c),
                      a = e.document.createTextNode("");
                    (s.observe(a, { characterData: !0 }),
                      (r = function () {
                        a.data = o = ++o % 2;
                      }));
                  } else if (e.setImmediate || void 0 === e.MessageChannel)
                    r =
                      "document" in e &&
                      "onreadystatechange" in e.document.createElement("script")
                        ? function () {
                            var t = e.document.createElement("script");
                            ((t.onreadystatechange = function () {
                              (c(),
                                (t.onreadystatechange = null),
                                t.parentNode.removeChild(t),
                                (t = null));
                            }),
                              e.document.documentElement.appendChild(t));
                          }
                        : function () {
                            setTimeout(c, 0);
                          };
                  else {
                    var d = new e.MessageChannel();
                    ((d.port1.onmessage = c),
                      (r = function () {
                        d.port2.postMessage(0);
                      }));
                  }
                  var l = [];
                  function c() {
                    var e, t;
                    n = !0;
                    for (var r = l.length; r; ) {
                      for (t = l, l = [], e = -1; ++e < r; ) t[e]();
                      r = l.length;
                    }
                    n = !1;
                  }
                  t.exports = function (e) {
                    1 !== l.push(e) || n || r();
                  };
                }).call(
                  this,
                  "undefined" != typeof global
                    ? global
                    : "undefined" != typeof self
                      ? self
                      : "undefined" != typeof window
                        ? window
                        : {},
                );
              },
              {},
            ],
            37: [
              function (e, t, r) {
                "use strict";
                var n = e("immediate");
                function i() {}
                var o = {},
                  s = ["REJECTED"],
                  a = ["FULFILLED"],
                  d = ["PENDING"];
                function l(e) {
                  if ("function" != typeof e)
                    throw new TypeError("resolver must be a function");
                  ((this.state = d),
                    (this.queue = []),
                    (this.outcome = void 0),
                    e !== i && h(this, e));
                }
                function c(e, t, r) {
                  ((this.promise = e),
                    "function" == typeof t &&
                      ((this.onFulfilled = t),
                      (this.callFulfilled = this.otherCallFulfilled)),
                    "function" == typeof r &&
                      ((this.onRejected = r),
                      (this.callRejected = this.otherCallRejected)));
                }
                function u(e, t, r) {
                  n(function () {
                    var n;
                    try {
                      n = t(r);
                    } catch (n) {
                      return o.reject(e, n);
                    }
                    n === e
                      ? o.reject(
                          e,
                          new TypeError("Cannot resolve promise with itself"),
                        )
                      : o.resolve(e, n);
                  });
                }
                function f(e) {
                  var t = e && e.then;
                  if (
                    e &&
                    ("object" == typeof e || "function" == typeof e) &&
                    "function" == typeof t
                  )
                    return function () {
                      t.apply(e, arguments);
                    };
                }
                function h(e, t) {
                  var r = !1;
                  function n(t) {
                    r || ((r = !0), o.reject(e, t));
                  }
                  function i(t) {
                    r || ((r = !0), o.resolve(e, t));
                  }
                  var s = m(function () {
                    t(i, n);
                  });
                  "error" === s.status && n(s.value);
                }
                function m(e, t) {
                  var r = {};
                  try {
                    ((r.value = e(t)), (r.status = "success"));
                  } catch (e) {
                    ((r.status = "error"), (r.value = e));
                  }
                  return r;
                }
                (((t.exports = l).prototype.finally = function (e) {
                  if ("function" != typeof e) return this;
                  var t = this.constructor;
                  return this.then(
                    function (r) {
                      return t.resolve(e()).then(function () {
                        return r;
                      });
                    },
                    function (r) {
                      return t.resolve(e()).then(function () {
                        throw r;
                      });
                    },
                  );
                }),
                  (l.prototype.catch = function (e) {
                    return this.then(null, e);
                  }),
                  (l.prototype.then = function (e, t) {
                    if (
                      ("function" != typeof e && this.state === a) ||
                      ("function" != typeof t && this.state === s)
                    )
                      return this;
                    var r = new this.constructor(i);
                    return (
                      this.state !== d
                        ? u(r, this.state === a ? e : t, this.outcome)
                        : this.queue.push(new c(r, e, t)),
                      r
                    );
                  }),
                  (c.prototype.callFulfilled = function (e) {
                    o.resolve(this.promise, e);
                  }),
                  (c.prototype.otherCallFulfilled = function (e) {
                    u(this.promise, this.onFulfilled, e);
                  }),
                  (c.prototype.callRejected = function (e) {
                    o.reject(this.promise, e);
                  }),
                  (c.prototype.otherCallRejected = function (e) {
                    u(this.promise, this.onRejected, e);
                  }),
                  (o.resolve = function (e, t) {
                    var r = m(f, t);
                    if ("error" === r.status) return o.reject(e, r.value);
                    var n = r.value;
                    if (n) h(e, n);
                    else {
                      ((e.state = a), (e.outcome = t));
                      for (var i = -1, s = e.queue.length; ++i < s; )
                        e.queue[i].callFulfilled(t);
                    }
                    return e;
                  }),
                  (o.reject = function (e, t) {
                    ((e.state = s), (e.outcome = t));
                    for (var r = -1, n = e.queue.length; ++r < n; )
                      e.queue[r].callRejected(t);
                    return e;
                  }),
                  (l.resolve = function (e) {
                    return e instanceof this ? e : o.resolve(new this(i), e);
                  }),
                  (l.reject = function (e) {
                    var t = new this(i);
                    return o.reject(t, e);
                  }),
                  (l.all = function (e) {
                    var t = this;
                    if ("[object Array]" !== Object.prototype.toString.call(e))
                      return this.reject(new TypeError("must be an array"));
                    var r = e.length,
                      n = !1;
                    if (!r) return this.resolve([]);
                    for (
                      var s = new Array(r), a = 0, d = -1, l = new this(i);
                      ++d < r;

                    )
                      c(e[d], d);
                    return l;
                    function c(e, i) {
                      t.resolve(e).then(
                        function (e) {
                          ((s[i] = e),
                            ++a !== r || n || ((n = !0), o.resolve(l, s)));
                        },
                        function (e) {
                          n || ((n = !0), o.reject(l, e));
                        },
                      );
                    }
                  }),
                  (l.race = function (e) {
                    var t = this;
                    if ("[object Array]" !== Object.prototype.toString.call(e))
                      return this.reject(new TypeError("must be an array"));
                    var r = e.length,
                      n = !1;
                    if (!r) return this.resolve([]);
                    for (var s, a = -1, d = new this(i); ++a < r; )
                      ((s = e[a]),
                        t.resolve(s).then(
                          function (e) {
                            n || ((n = !0), o.resolve(d, e));
                          },
                          function (e) {
                            n || ((n = !0), o.reject(d, e));
                          },
                        ));
                    return d;
                  }));
              },
              { immediate: 36 },
            ],
            38: [
              function (e, t, r) {
                "use strict";
                var n = {};
                ((0, e("./lib/utils/common").assign)(
                  n,
                  e("./lib/deflate"),
                  e("./lib/inflate"),
                  e("./lib/zlib/constants"),
                ),
                  (t.exports = n));
              },
              {
                "./lib/deflate": 39,
                "./lib/inflate": 40,
                "./lib/utils/common": 41,
                "./lib/zlib/constants": 44,
              },
            ],
            39: [
              function (e, t, r) {
                "use strict";
                var n = e("./zlib/deflate"),
                  i = e("./utils/common"),
                  o = e("./utils/strings"),
                  s = e("./zlib/messages"),
                  a = e("./zlib/zstream"),
                  d = Object.prototype.toString,
                  l = 0,
                  c = -1,
                  u = 0,
                  f = 8;
                function h(e) {
                  if (!(this instanceof h)) return new h(e);
                  this.options = i.assign(
                    {
                      level: c,
                      method: f,
                      chunkSize: 16384,
                      windowBits: 15,
                      memLevel: 8,
                      strategy: u,
                      to: "",
                    },
                    e || {},
                  );
                  var t = this.options;
                  (t.raw && 0 < t.windowBits
                    ? (t.windowBits = -t.windowBits)
                    : t.gzip &&
                      0 < t.windowBits &&
                      t.windowBits < 16 &&
                      (t.windowBits += 16),
                    (this.err = 0),
                    (this.msg = ""),
                    (this.ended = !1),
                    (this.chunks = []),
                    (this.strm = new a()),
                    (this.strm.avail_out = 0));
                  var r = n.deflateInit2(
                    this.strm,
                    t.level,
                    t.method,
                    t.windowBits,
                    t.memLevel,
                    t.strategy,
                  );
                  if (r !== l) throw new Error(s[r]);
                  if (
                    (t.header && n.deflateSetHeader(this.strm, t.header),
                    t.dictionary)
                  ) {
                    var m;
                    if (
                      ((m =
                        "string" == typeof t.dictionary
                          ? o.string2buf(t.dictionary)
                          : "[object ArrayBuffer]" === d.call(t.dictionary)
                            ? new Uint8Array(t.dictionary)
                            : t.dictionary),
                      (r = n.deflateSetDictionary(this.strm, m)) !== l)
                    )
                      throw new Error(s[r]);
                    this._dict_set = !0;
                  }
                }
                function m(e, t) {
                  var r = new h(t);
                  if ((r.push(e, !0), r.err)) throw r.msg || s[r.err];
                  return r.result;
                }
                ((h.prototype.push = function (e, t) {
                  var r,
                    s,
                    a = this.strm,
                    c = this.options.chunkSize;
                  if (this.ended) return !1;
                  ((s = t === ~~t ? t : !0 === t ? 4 : 0),
                    "string" == typeof e
                      ? (a.input = o.string2buf(e))
                      : "[object ArrayBuffer]" === d.call(e)
                        ? (a.input = new Uint8Array(e))
                        : (a.input = e),
                    (a.next_in = 0),
                    (a.avail_in = a.input.length));
                  do {
                    if (
                      (0 === a.avail_out &&
                        ((a.output = new i.Buf8(c)),
                        (a.next_out = 0),
                        (a.avail_out = c)),
                      1 !== (r = n.deflate(a, s)) && r !== l)
                    )
                      return (this.onEnd(r), !(this.ended = !0));
                    (0 !== a.avail_out &&
                      (0 !== a.avail_in || (4 !== s && 2 !== s))) ||
                      ("string" === this.options.to
                        ? this.onData(
                            o.buf2binstring(i.shrinkBuf(a.output, a.next_out)),
                          )
                        : this.onData(i.shrinkBuf(a.output, a.next_out)));
                  } while ((0 < a.avail_in || 0 === a.avail_out) && 1 !== r);
                  return 4 === s
                    ? ((r = n.deflateEnd(this.strm)),
                      this.onEnd(r),
                      (this.ended = !0),
                      r === l)
                    : 2 !== s || (this.onEnd(l), !(a.avail_out = 0));
                }),
                  (h.prototype.onData = function (e) {
                    this.chunks.push(e);
                  }),
                  (h.prototype.onEnd = function (e) {
                    (e === l &&
                      ("string" === this.options.to
                        ? (this.result = this.chunks.join(""))
                        : (this.result = i.flattenChunks(this.chunks))),
                      (this.chunks = []),
                      (this.err = e),
                      (this.msg = this.strm.msg));
                  }),
                  (r.Deflate = h),
                  (r.deflate = m),
                  (r.deflateRaw = function (e, t) {
                    return (((t = t || {}).raw = !0), m(e, t));
                  }),
                  (r.gzip = function (e, t) {
                    return (((t = t || {}).gzip = !0), m(e, t));
                  }));
              },
              {
                "./utils/common": 41,
                "./utils/strings": 42,
                "./zlib/deflate": 46,
                "./zlib/messages": 51,
                "./zlib/zstream": 53,
              },
            ],
            40: [
              function (e, t, r) {
                "use strict";
                var n = e("./zlib/inflate"),
                  i = e("./utils/common"),
                  o = e("./utils/strings"),
                  s = e("./zlib/constants"),
                  a = e("./zlib/messages"),
                  d = e("./zlib/zstream"),
                  l = e("./zlib/gzheader"),
                  c = Object.prototype.toString;
                function u(e) {
                  if (!(this instanceof u)) return new u(e);
                  this.options = i.assign(
                    { chunkSize: 16384, windowBits: 0, to: "" },
                    e || {},
                  );
                  var t = this.options;
                  (t.raw &&
                    0 <= t.windowBits &&
                    t.windowBits < 16 &&
                    ((t.windowBits = -t.windowBits),
                    0 === t.windowBits && (t.windowBits = -15)),
                    !(0 <= t.windowBits && t.windowBits < 16) ||
                      (e && e.windowBits) ||
                      (t.windowBits += 32),
                    15 < t.windowBits &&
                      t.windowBits < 48 &&
                      !(15 & t.windowBits) &&
                      (t.windowBits |= 15),
                    (this.err = 0),
                    (this.msg = ""),
                    (this.ended = !1),
                    (this.chunks = []),
                    (this.strm = new d()),
                    (this.strm.avail_out = 0));
                  var r = n.inflateInit2(this.strm, t.windowBits);
                  if (r !== s.Z_OK) throw new Error(a[r]);
                  ((this.header = new l()),
                    n.inflateGetHeader(this.strm, this.header));
                }
                function f(e, t) {
                  var r = new u(t);
                  if ((r.push(e, !0), r.err)) throw r.msg || a[r.err];
                  return r.result;
                }
                ((u.prototype.push = function (e, t) {
                  var r,
                    a,
                    d,
                    l,
                    u,
                    f,
                    h = this.strm,
                    m = this.options.chunkSize,
                    p = this.options.dictionary,
                    g = !1;
                  if (this.ended) return !1;
                  ((a = t === ~~t ? t : !0 === t ? s.Z_FINISH : s.Z_NO_FLUSH),
                    "string" == typeof e
                      ? (h.input = o.binstring2buf(e))
                      : "[object ArrayBuffer]" === c.call(e)
                        ? (h.input = new Uint8Array(e))
                        : (h.input = e),
                    (h.next_in = 0),
                    (h.avail_in = h.input.length));
                  do {
                    if (
                      (0 === h.avail_out &&
                        ((h.output = new i.Buf8(m)),
                        (h.next_out = 0),
                        (h.avail_out = m)),
                      (r = n.inflate(h, s.Z_NO_FLUSH)) === s.Z_NEED_DICT &&
                        p &&
                        ((f =
                          "string" == typeof p
                            ? o.string2buf(p)
                            : "[object ArrayBuffer]" === c.call(p)
                              ? new Uint8Array(p)
                              : p),
                        (r = n.inflateSetDictionary(this.strm, f))),
                      r === s.Z_BUF_ERROR &&
                        !0 === g &&
                        ((r = s.Z_OK), (g = !1)),
                      r !== s.Z_STREAM_END && r !== s.Z_OK)
                    )
                      return (this.onEnd(r), !(this.ended = !0));
                    (h.next_out &&
                      ((0 !== h.avail_out &&
                        r !== s.Z_STREAM_END &&
                        (0 !== h.avail_in ||
                          (a !== s.Z_FINISH && a !== s.Z_SYNC_FLUSH))) ||
                        ("string" === this.options.to
                          ? ((d = o.utf8border(h.output, h.next_out)),
                            (l = h.next_out - d),
                            (u = o.buf2string(h.output, d)),
                            (h.next_out = l),
                            (h.avail_out = m - l),
                            l && i.arraySet(h.output, h.output, d, l, 0),
                            this.onData(u))
                          : this.onData(i.shrinkBuf(h.output, h.next_out)))),
                      0 === h.avail_in && 0 === h.avail_out && (g = !0));
                  } while (
                    (0 < h.avail_in || 0 === h.avail_out) &&
                    r !== s.Z_STREAM_END
                  );
                  return (
                    r === s.Z_STREAM_END && (a = s.Z_FINISH),
                    a === s.Z_FINISH
                      ? ((r = n.inflateEnd(this.strm)),
                        this.onEnd(r),
                        (this.ended = !0),
                        r === s.Z_OK)
                      : a !== s.Z_SYNC_FLUSH ||
                        (this.onEnd(s.Z_OK), !(h.avail_out = 0))
                  );
                }),
                  (u.prototype.onData = function (e) {
                    this.chunks.push(e);
                  }),
                  (u.prototype.onEnd = function (e) {
                    (e === s.Z_OK &&
                      ("string" === this.options.to
                        ? (this.result = this.chunks.join(""))
                        : (this.result = i.flattenChunks(this.chunks))),
                      (this.chunks = []),
                      (this.err = e),
                      (this.msg = this.strm.msg));
                  }),
                  (r.Inflate = u),
                  (r.inflate = f),
                  (r.inflateRaw = function (e, t) {
                    return (((t = t || {}).raw = !0), f(e, t));
                  }),
                  (r.ungzip = f));
              },
              {
                "./utils/common": 41,
                "./utils/strings": 42,
                "./zlib/constants": 44,
                "./zlib/gzheader": 47,
                "./zlib/inflate": 49,
                "./zlib/messages": 51,
                "./zlib/zstream": 53,
              },
            ],
            41: [
              function (e, t, r) {
                "use strict";
                var n =
                  "undefined" != typeof Uint8Array &&
                  "undefined" != typeof Uint16Array &&
                  "undefined" != typeof Int32Array;
                ((r.assign = function (e) {
                  for (
                    var t = Array.prototype.slice.call(arguments, 1);
                    t.length;

                  ) {
                    var r = t.shift();
                    if (r) {
                      if ("object" != typeof r)
                        throw new TypeError(r + "must be non-object");
                      for (var n in r) r.hasOwnProperty(n) && (e[n] = r[n]);
                    }
                  }
                  return e;
                }),
                  (r.shrinkBuf = function (e, t) {
                    return e.length === t
                      ? e
                      : e.subarray
                        ? e.subarray(0, t)
                        : ((e.length = t), e);
                  }));
                var i = {
                    arraySet: function (e, t, r, n, i) {
                      if (t.subarray && e.subarray)
                        e.set(t.subarray(r, r + n), i);
                      else for (var o = 0; o < n; o++) e[i + o] = t[r + o];
                    },
                    flattenChunks: function (e) {
                      var t, r, n, i, o, s;
                      for (t = n = 0, r = e.length; t < r; t++)
                        n += e[t].length;
                      for (
                        s = new Uint8Array(n), t = i = 0, r = e.length;
                        t < r;
                        t++
                      )
                        ((o = e[t]), s.set(o, i), (i += o.length));
                      return s;
                    },
                  },
                  o = {
                    arraySet: function (e, t, r, n, i) {
                      for (var o = 0; o < n; o++) e[i + o] = t[r + o];
                    },
                    flattenChunks: function (e) {
                      return [].concat.apply([], e);
                    },
                  };
                ((r.setTyped = function (e) {
                  e
                    ? ((r.Buf8 = Uint8Array),
                      (r.Buf16 = Uint16Array),
                      (r.Buf32 = Int32Array),
                      r.assign(r, i))
                    : ((r.Buf8 = Array),
                      (r.Buf16 = Array),
                      (r.Buf32 = Array),
                      r.assign(r, o));
                }),
                  r.setTyped(n));
              },
              {},
            ],
            42: [
              function (e, t, r) {
                "use strict";
                var n = e("./common"),
                  i = !0,
                  o = !0;
                try {
                  String.fromCharCode.apply(null, [0]);
                } catch (e) {
                  i = !1;
                }
                try {
                  String.fromCharCode.apply(null, new Uint8Array(1));
                } catch (e) {
                  o = !1;
                }
                for (var s = new n.Buf8(256), a = 0; a < 256; a++)
                  s[a] =
                    252 <= a
                      ? 6
                      : 248 <= a
                        ? 5
                        : 240 <= a
                          ? 4
                          : 224 <= a
                            ? 3
                            : 192 <= a
                              ? 2
                              : 1;
                function d(e, t) {
                  if (t < 65537 && ((e.subarray && o) || (!e.subarray && i)))
                    return String.fromCharCode.apply(null, n.shrinkBuf(e, t));
                  for (var r = "", s = 0; s < t; s++)
                    r += String.fromCharCode(e[s]);
                  return r;
                }
                ((s[254] = s[254] = 1),
                  (r.string2buf = function (e) {
                    var t,
                      r,
                      i,
                      o,
                      s,
                      a = e.length,
                      d = 0;
                    for (o = 0; o < a; o++)
                      (55296 == (64512 & (r = e.charCodeAt(o))) &&
                        o + 1 < a &&
                        56320 == (64512 & (i = e.charCodeAt(o + 1))) &&
                        ((r = 65536 + ((r - 55296) << 10) + (i - 56320)), o++),
                        (d += r < 128 ? 1 : r < 2048 ? 2 : r < 65536 ? 3 : 4));
                    for (t = new n.Buf8(d), o = s = 0; s < d; o++)
                      (55296 == (64512 & (r = e.charCodeAt(o))) &&
                        o + 1 < a &&
                        56320 == (64512 & (i = e.charCodeAt(o + 1))) &&
                        ((r = 65536 + ((r - 55296) << 10) + (i - 56320)), o++),
                        r < 128
                          ? (t[s++] = r)
                          : (r < 2048
                              ? (t[s++] = 192 | (r >>> 6))
                              : (r < 65536
                                  ? (t[s++] = 224 | (r >>> 12))
                                  : ((t[s++] = 240 | (r >>> 18)),
                                    (t[s++] = 128 | ((r >>> 12) & 63))),
                                (t[s++] = 128 | ((r >>> 6) & 63))),
                            (t[s++] = 128 | (63 & r))));
                    return t;
                  }),
                  (r.buf2binstring = function (e) {
                    return d(e, e.length);
                  }),
                  (r.binstring2buf = function (e) {
                    for (
                      var t = new n.Buf8(e.length), r = 0, i = t.length;
                      r < i;
                      r++
                    )
                      t[r] = e.charCodeAt(r);
                    return t;
                  }),
                  (r.buf2string = function (e, t) {
                    var r,
                      n,
                      i,
                      o,
                      a = t || e.length,
                      l = new Array(2 * a);
                    for (r = n = 0; r < a; )
                      if ((i = e[r++]) < 128) l[n++] = i;
                      else if (4 < (o = s[i])) ((l[n++] = 65533), (r += o - 1));
                      else {
                        for (
                          i &= 2 === o ? 31 : 3 === o ? 15 : 7;
                          1 < o && r < a;

                        )
                          ((i = (i << 6) | (63 & e[r++])), o--);
                        1 < o
                          ? (l[n++] = 65533)
                          : i < 65536
                            ? (l[n++] = i)
                            : ((i -= 65536),
                              (l[n++] = 55296 | ((i >> 10) & 1023)),
                              (l[n++] = 56320 | (1023 & i)));
                      }
                    return d(l, n);
                  }),
                  (r.utf8border = function (e, t) {
                    var r;
                    for (
                      (t = t || e.length) > e.length && (t = e.length),
                        r = t - 1;
                      0 <= r && 128 == (192 & e[r]);

                    )
                      r--;
                    return r < 0 || 0 === r ? t : r + s[e[r]] > t ? r : t;
                  }));
              },
              { "./common": 41 },
            ],
            43: [
              function (e, t, r) {
                "use strict";
                t.exports = function (e, t, r, n) {
                  for (
                    var i = 65535 & e, o = (e >>> 16) & 65535, s = 0;
                    0 !== r;

                  ) {
                    for (
                      r -= s = 2e3 < r ? 2e3 : r;
                      (o = (o + (i = (i + t[n++]) | 0)) | 0), --s;

                    );
                    ((i %= 65521), (o %= 65521));
                  }
                  return i | (o << 16);
                };
              },
              {},
            ],
            44: [
              function (e, t, r) {
                "use strict";
                t.exports = {
                  Z_NO_FLUSH: 0,
                  Z_PARTIAL_FLUSH: 1,
                  Z_SYNC_FLUSH: 2,
                  Z_FULL_FLUSH: 3,
                  Z_FINISH: 4,
                  Z_BLOCK: 5,
                  Z_TREES: 6,
                  Z_OK: 0,
                  Z_STREAM_END: 1,
                  Z_NEED_DICT: 2,
                  Z_ERRNO: -1,
                  Z_STREAM_ERROR: -2,
                  Z_DATA_ERROR: -3,
                  Z_BUF_ERROR: -5,
                  Z_NO_COMPRESSION: 0,
                  Z_BEST_SPEED: 1,
                  Z_BEST_COMPRESSION: 9,
                  Z_DEFAULT_COMPRESSION: -1,
                  Z_FILTERED: 1,
                  Z_HUFFMAN_ONLY: 2,
                  Z_RLE: 3,
                  Z_FIXED: 4,
                  Z_DEFAULT_STRATEGY: 0,
                  Z_BINARY: 0,
                  Z_TEXT: 1,
                  Z_UNKNOWN: 2,
                  Z_DEFLATED: 8,
                };
              },
              {},
            ],
            45: [
              function (e, t, r) {
                "use strict";
                var n = (function () {
                  for (var e, t = [], r = 0; r < 256; r++) {
                    e = r;
                    for (var n = 0; n < 8; n++)
                      e = 1 & e ? 3988292384 ^ (e >>> 1) : e >>> 1;
                    t[r] = e;
                  }
                  return t;
                })();
                t.exports = function (e, t, r, i) {
                  var o = n,
                    s = i + r;
                  e ^= -1;
                  for (var a = i; a < s; a++)
                    e = (e >>> 8) ^ o[255 & (e ^ t[a])];
                  return ~e;
                };
              },
              {},
            ],
            46: [
              function (e, t, r) {
                "use strict";
                var n,
                  i = e("../utils/common"),
                  o = e("./trees"),
                  s = e("./adler32"),
                  a = e("./crc32"),
                  d = e("./messages"),
                  l = 0,
                  c = 4,
                  u = 0,
                  f = -2,
                  h = -1,
                  m = 4,
                  p = 2,
                  g = 8,
                  w = 9,
                  y = 286,
                  v = 30,
                  b = 19,
                  _ = 2 * y + 1,
                  x = 15,
                  A = 3,
                  k = 258,
                  S = k + A + 1,
                  I = 42,
                  C = 113,
                  O = 1,
                  E = 2,
                  D = 3,
                  T = 4;
                function U(e, t) {
                  return ((e.msg = d[t]), t);
                }
                function z(e) {
                  return (e << 1) - (4 < e ? 9 : 0);
                }
                function B(e) {
                  for (var t = e.length; 0 <= --t; ) e[t] = 0;
                }
                function L(e) {
                  var t = e.state,
                    r = t.pending;
                  (r > e.avail_out && (r = e.avail_out),
                    0 !== r &&
                      (i.arraySet(
                        e.output,
                        t.pending_buf,
                        t.pending_out,
                        r,
                        e.next_out,
                      ),
                      (e.next_out += r),
                      (t.pending_out += r),
                      (e.total_out += r),
                      (e.avail_out -= r),
                      (t.pending -= r),
                      0 === t.pending && (t.pending_out = 0)));
                }
                function M(e, t) {
                  (o._tr_flush_block(
                    e,
                    0 <= e.block_start ? e.block_start : -1,
                    e.strstart - e.block_start,
                    t,
                  ),
                    (e.block_start = e.strstart),
                    L(e.strm));
                }
                function R(e, t) {
                  e.pending_buf[e.pending++] = t;
                }
                function P(e, t) {
                  ((e.pending_buf[e.pending++] = (t >>> 8) & 255),
                    (e.pending_buf[e.pending++] = 255 & t));
                }
                function N(e, t) {
                  var r,
                    n,
                    i = e.max_chain_length,
                    o = e.strstart,
                    s = e.prev_length,
                    a = e.nice_match,
                    d =
                      e.strstart > e.w_size - S
                        ? e.strstart - (e.w_size - S)
                        : 0,
                    l = e.window,
                    c = e.w_mask,
                    u = e.prev,
                    f = e.strstart + k,
                    h = l[o + s - 1],
                    m = l[o + s];
                  (e.prev_length >= e.good_match && (i >>= 2),
                    a > e.lookahead && (a = e.lookahead));
                  do {
                    if (
                      l[(r = t) + s] === m &&
                      l[r + s - 1] === h &&
                      l[r] === l[o] &&
                      l[++r] === l[o + 1]
                    ) {
                      ((o += 2), r++);
                      do {} while (
                        l[++o] === l[++r] &&
                        l[++o] === l[++r] &&
                        l[++o] === l[++r] &&
                        l[++o] === l[++r] &&
                        l[++o] === l[++r] &&
                        l[++o] === l[++r] &&
                        l[++o] === l[++r] &&
                        l[++o] === l[++r] &&
                        o < f
                      );
                      if (((n = k - (f - o)), (o = f - k), s < n)) {
                        if (((e.match_start = t), a <= (s = n))) break;
                        ((h = l[o + s - 1]), (m = l[o + s]));
                      }
                    }
                  } while ((t = u[t & c]) > d && 0 != --i);
                  return s <= e.lookahead ? s : e.lookahead;
                }
                function F(e) {
                  var t,
                    r,
                    n,
                    o,
                    d,
                    l,
                    c,
                    u,
                    f,
                    h,
                    m = e.w_size;
                  do {
                    if (
                      ((o = e.window_size - e.lookahead - e.strstart),
                      e.strstart >= m + (m - S))
                    ) {
                      for (
                        i.arraySet(e.window, e.window, m, m, 0),
                          e.match_start -= m,
                          e.strstart -= m,
                          e.block_start -= m,
                          t = r = e.hash_size;
                        (n = e.head[--t]),
                          (e.head[t] = m <= n ? n - m : 0),
                          --r;

                      );
                      for (
                        t = r = m;
                        (n = e.prev[--t]),
                          (e.prev[t] = m <= n ? n - m : 0),
                          --r;

                      );
                      o += m;
                    }
                    if (0 === e.strm.avail_in) break;
                    if (
                      ((l = e.strm),
                      (c = e.window),
                      (u = e.strstart + e.lookahead),
                      (h = void 0),
                      (f = o) < (h = l.avail_in) && (h = f),
                      (r =
                        0 === h
                          ? 0
                          : ((l.avail_in -= h),
                            i.arraySet(c, l.input, l.next_in, h, u),
                            1 === l.state.wrap
                              ? (l.adler = s(l.adler, c, h, u))
                              : 2 === l.state.wrap &&
                                (l.adler = a(l.adler, c, h, u)),
                            (l.next_in += h),
                            (l.total_in += h),
                            h)),
                      (e.lookahead += r),
                      e.lookahead + e.insert >= A)
                    )
                      for (
                        d = e.strstart - e.insert,
                          e.ins_h = e.window[d],
                          e.ins_h =
                            ((e.ins_h << e.hash_shift) ^ e.window[d + 1]) &
                            e.hash_mask;
                        e.insert &&
                        ((e.ins_h =
                          ((e.ins_h << e.hash_shift) ^ e.window[d + A - 1]) &
                          e.hash_mask),
                        (e.prev[d & e.w_mask] = e.head[e.ins_h]),
                        (e.head[e.ins_h] = d),
                        d++,
                        e.insert--,
                        !(e.lookahead + e.insert < A));

                      );
                  } while (e.lookahead < S && 0 !== e.strm.avail_in);
                }
                function j(e, t) {
                  for (var r, n; ; ) {
                    if (e.lookahead < S) {
                      if ((F(e), e.lookahead < S && t === l)) return O;
                      if (0 === e.lookahead) break;
                    }
                    if (
                      ((r = 0),
                      e.lookahead >= A &&
                        ((e.ins_h =
                          ((e.ins_h << e.hash_shift) ^
                            e.window[e.strstart + A - 1]) &
                          e.hash_mask),
                        (r = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h]),
                        (e.head[e.ins_h] = e.strstart)),
                      0 !== r &&
                        e.strstart - r <= e.w_size - S &&
                        (e.match_length = N(e, r)),
                      e.match_length >= A)
                    )
                      if (
                        ((n = o._tr_tally(
                          e,
                          e.strstart - e.match_start,
                          e.match_length - A,
                        )),
                        (e.lookahead -= e.match_length),
                        e.match_length <= e.max_lazy_match && e.lookahead >= A)
                      ) {
                        for (
                          e.match_length--;
                          e.strstart++,
                            (e.ins_h =
                              ((e.ins_h << e.hash_shift) ^
                                e.window[e.strstart + A - 1]) &
                              e.hash_mask),
                            (r = e.prev[e.strstart & e.w_mask] =
                              e.head[e.ins_h]),
                            (e.head[e.ins_h] = e.strstart),
                            0 != --e.match_length;

                        );
                        e.strstart++;
                      } else
                        ((e.strstart += e.match_length),
                          (e.match_length = 0),
                          (e.ins_h = e.window[e.strstart]),
                          (e.ins_h =
                            ((e.ins_h << e.hash_shift) ^
                              e.window[e.strstart + 1]) &
                            e.hash_mask));
                    else
                      ((n = o._tr_tally(e, 0, e.window[e.strstart])),
                        e.lookahead--,
                        e.strstart++);
                    if (n && (M(e, !1), 0 === e.strm.avail_out)) return O;
                  }
                  return (
                    (e.insert = e.strstart < A - 1 ? e.strstart : A - 1),
                    t === c
                      ? (M(e, !0), 0 === e.strm.avail_out ? D : T)
                      : e.last_lit && (M(e, !1), 0 === e.strm.avail_out)
                        ? O
                        : E
                  );
                }
                function W(e, t) {
                  for (var r, n, i; ; ) {
                    if (e.lookahead < S) {
                      if ((F(e), e.lookahead < S && t === l)) return O;
                      if (0 === e.lookahead) break;
                    }
                    if (
                      ((r = 0),
                      e.lookahead >= A &&
                        ((e.ins_h =
                          ((e.ins_h << e.hash_shift) ^
                            e.window[e.strstart + A - 1]) &
                          e.hash_mask),
                        (r = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h]),
                        (e.head[e.ins_h] = e.strstart)),
                      (e.prev_length = e.match_length),
                      (e.prev_match = e.match_start),
                      (e.match_length = A - 1),
                      0 !== r &&
                        e.prev_length < e.max_lazy_match &&
                        e.strstart - r <= e.w_size - S &&
                        ((e.match_length = N(e, r)),
                        e.match_length <= 5 &&
                          (1 === e.strategy ||
                            (e.match_length === A &&
                              4096 < e.strstart - e.match_start)) &&
                          (e.match_length = A - 1)),
                      e.prev_length >= A && e.match_length <= e.prev_length)
                    ) {
                      for (
                        i = e.strstart + e.lookahead - A,
                          n = o._tr_tally(
                            e,
                            e.strstart - 1 - e.prev_match,
                            e.prev_length - A,
                          ),
                          e.lookahead -= e.prev_length - 1,
                          e.prev_length -= 2;
                        ++e.strstart <= i &&
                          ((e.ins_h =
                            ((e.ins_h << e.hash_shift) ^
                              e.window[e.strstart + A - 1]) &
                            e.hash_mask),
                          (r = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h]),
                          (e.head[e.ins_h] = e.strstart)),
                          0 != --e.prev_length;

                      );
                      if (
                        ((e.match_available = 0),
                        (e.match_length = A - 1),
                        e.strstart++,
                        n && (M(e, !1), 0 === e.strm.avail_out))
                      )
                        return O;
                    } else if (e.match_available) {
                      if (
                        ((n = o._tr_tally(e, 0, e.window[e.strstart - 1])) &&
                          M(e, !1),
                        e.strstart++,
                        e.lookahead--,
                        0 === e.strm.avail_out)
                      )
                        return O;
                    } else
                      ((e.match_available = 1), e.strstart++, e.lookahead--);
                  }
                  return (
                    e.match_available &&
                      ((n = o._tr_tally(e, 0, e.window[e.strstart - 1])),
                      (e.match_available = 0)),
                    (e.insert = e.strstart < A - 1 ? e.strstart : A - 1),
                    t === c
                      ? (M(e, !0), 0 === e.strm.avail_out ? D : T)
                      : e.last_lit && (M(e, !1), 0 === e.strm.avail_out)
                        ? O
                        : E
                  );
                }
                function Z(e, t, r, n, i) {
                  ((this.good_length = e),
                    (this.max_lazy = t),
                    (this.nice_length = r),
                    (this.max_chain = n),
                    (this.func = i));
                }
                function $() {
                  ((this.strm = null),
                    (this.status = 0),
                    (this.pending_buf = null),
                    (this.pending_buf_size = 0),
                    (this.pending_out = 0),
                    (this.pending = 0),
                    (this.wrap = 0),
                    (this.gzhead = null),
                    (this.gzindex = 0),
                    (this.method = g),
                    (this.last_flush = -1),
                    (this.w_size = 0),
                    (this.w_bits = 0),
                    (this.w_mask = 0),
                    (this.window = null),
                    (this.window_size = 0),
                    (this.prev = null),
                    (this.head = null),
                    (this.ins_h = 0),
                    (this.hash_size = 0),
                    (this.hash_bits = 0),
                    (this.hash_mask = 0),
                    (this.hash_shift = 0),
                    (this.block_start = 0),
                    (this.match_length = 0),
                    (this.prev_match = 0),
                    (this.match_available = 0),
                    (this.strstart = 0),
                    (this.match_start = 0),
                    (this.lookahead = 0),
                    (this.prev_length = 0),
                    (this.max_chain_length = 0),
                    (this.max_lazy_match = 0),
                    (this.level = 0),
                    (this.strategy = 0),
                    (this.good_match = 0),
                    (this.nice_match = 0),
                    (this.dyn_ltree = new i.Buf16(2 * _)),
                    (this.dyn_dtree = new i.Buf16(2 * (2 * v + 1))),
                    (this.bl_tree = new i.Buf16(2 * (2 * b + 1))),
                    B(this.dyn_ltree),
                    B(this.dyn_dtree),
                    B(this.bl_tree),
                    (this.l_desc = null),
                    (this.d_desc = null),
                    (this.bl_desc = null),
                    (this.bl_count = new i.Buf16(x + 1)),
                    (this.heap = new i.Buf16(2 * y + 1)),
                    B(this.heap),
                    (this.heap_len = 0),
                    (this.heap_max = 0),
                    (this.depth = new i.Buf16(2 * y + 1)),
                    B(this.depth),
                    (this.l_buf = 0),
                    (this.lit_bufsize = 0),
                    (this.last_lit = 0),
                    (this.d_buf = 0),
                    (this.opt_len = 0),
                    (this.static_len = 0),
                    (this.matches = 0),
                    (this.insert = 0),
                    (this.bi_buf = 0),
                    (this.bi_valid = 0));
                }
                function V(e) {
                  var t;
                  return e && e.state
                    ? ((e.total_in = e.total_out = 0),
                      (e.data_type = p),
                      ((t = e.state).pending = 0),
                      (t.pending_out = 0),
                      t.wrap < 0 && (t.wrap = -t.wrap),
                      (t.status = t.wrap ? I : C),
                      (e.adler = 2 === t.wrap ? 0 : 1),
                      (t.last_flush = l),
                      o._tr_init(t),
                      u)
                    : U(e, f);
                }
                function K(e) {
                  var t = V(e);
                  return (
                    t === u &&
                      (function (e) {
                        ((e.window_size = 2 * e.w_size),
                          B(e.head),
                          (e.max_lazy_match = n[e.level].max_lazy),
                          (e.good_match = n[e.level].good_length),
                          (e.nice_match = n[e.level].nice_length),
                          (e.max_chain_length = n[e.level].max_chain),
                          (e.strstart = 0),
                          (e.block_start = 0),
                          (e.lookahead = 0),
                          (e.insert = 0),
                          (e.match_length = e.prev_length = A - 1),
                          (e.match_available = 0),
                          (e.ins_h = 0));
                      })(e.state),
                    t
                  );
                }
                function H(e, t, r, n, o, s) {
                  if (!e) return f;
                  var a = 1;
                  if (
                    (t === h && (t = 6),
                    n < 0
                      ? ((a = 0), (n = -n))
                      : 15 < n && ((a = 2), (n -= 16)),
                    o < 1 ||
                      w < o ||
                      r !== g ||
                      n < 8 ||
                      15 < n ||
                      t < 0 ||
                      9 < t ||
                      s < 0 ||
                      m < s)
                  )
                    return U(e, f);
                  8 === n && (n = 9);
                  var d = new $();
                  return (
                    ((e.state = d).strm = e),
                    (d.wrap = a),
                    (d.gzhead = null),
                    (d.w_bits = n),
                    (d.w_size = 1 << d.w_bits),
                    (d.w_mask = d.w_size - 1),
                    (d.hash_bits = o + 7),
                    (d.hash_size = 1 << d.hash_bits),
                    (d.hash_mask = d.hash_size - 1),
                    (d.hash_shift = ~~((d.hash_bits + A - 1) / A)),
                    (d.window = new i.Buf8(2 * d.w_size)),
                    (d.head = new i.Buf16(d.hash_size)),
                    (d.prev = new i.Buf16(d.w_size)),
                    (d.lit_bufsize = 1 << (o + 6)),
                    (d.pending_buf_size = 4 * d.lit_bufsize),
                    (d.pending_buf = new i.Buf8(d.pending_buf_size)),
                    (d.d_buf = 1 * d.lit_bufsize),
                    (d.l_buf = 3 * d.lit_bufsize),
                    (d.level = t),
                    (d.strategy = s),
                    (d.method = r),
                    K(e)
                  );
                }
                ((n = [
                  new Z(0, 0, 0, 0, function (e, t) {
                    var r = 65535;
                    for (
                      r > e.pending_buf_size - 5 &&
                      (r = e.pending_buf_size - 5);
                      ;

                    ) {
                      if (e.lookahead <= 1) {
                        if ((F(e), 0 === e.lookahead && t === l)) return O;
                        if (0 === e.lookahead) break;
                      }
                      ((e.strstart += e.lookahead), (e.lookahead = 0));
                      var n = e.block_start + r;
                      if (
                        (0 === e.strstart || e.strstart >= n) &&
                        ((e.lookahead = e.strstart - n),
                        (e.strstart = n),
                        M(e, !1),
                        0 === e.strm.avail_out)
                      )
                        return O;
                      if (
                        e.strstart - e.block_start >= e.w_size - S &&
                        (M(e, !1), 0 === e.strm.avail_out)
                      )
                        return O;
                    }
                    return (
                      (e.insert = 0),
                      t === c
                        ? (M(e, !0), 0 === e.strm.avail_out ? D : T)
                        : (e.strstart > e.block_start &&
                            (M(e, !1), e.strm.avail_out),
                          O)
                    );
                  }),
                  new Z(4, 4, 8, 4, j),
                  new Z(4, 5, 16, 8, j),
                  new Z(4, 6, 32, 32, j),
                  new Z(4, 4, 16, 16, W),
                  new Z(8, 16, 32, 32, W),
                  new Z(8, 16, 128, 128, W),
                  new Z(8, 32, 128, 256, W),
                  new Z(32, 128, 258, 1024, W),
                  new Z(32, 258, 258, 4096, W),
                ]),
                  (r.deflateInit = function (e, t) {
                    return H(e, t, g, 15, 8, 0);
                  }),
                  (r.deflateInit2 = H),
                  (r.deflateReset = K),
                  (r.deflateResetKeep = V),
                  (r.deflateSetHeader = function (e, t) {
                    return e && e.state
                      ? 2 !== e.state.wrap
                        ? f
                        : ((e.state.gzhead = t), u)
                      : f;
                  }),
                  (r.deflate = function (e, t) {
                    var r, i, s, d;
                    if (!e || !e.state || 5 < t || t < 0)
                      return e ? U(e, f) : f;
                    if (
                      ((i = e.state),
                      !e.output ||
                        (!e.input && 0 !== e.avail_in) ||
                        (666 === i.status && t !== c))
                    )
                      return U(e, 0 === e.avail_out ? -5 : f);
                    if (
                      ((i.strm = e),
                      (r = i.last_flush),
                      (i.last_flush = t),
                      i.status === I)
                    )
                      if (2 === i.wrap)
                        ((e.adler = 0),
                          R(i, 31),
                          R(i, 139),
                          R(i, 8),
                          i.gzhead
                            ? (R(
                                i,
                                (i.gzhead.text ? 1 : 0) +
                                  (i.gzhead.hcrc ? 2 : 0) +
                                  (i.gzhead.extra ? 4 : 0) +
                                  (i.gzhead.name ? 8 : 0) +
                                  (i.gzhead.comment ? 16 : 0),
                              ),
                              R(i, 255 & i.gzhead.time),
                              R(i, (i.gzhead.time >> 8) & 255),
                              R(i, (i.gzhead.time >> 16) & 255),
                              R(i, (i.gzhead.time >> 24) & 255),
                              R(
                                i,
                                9 === i.level
                                  ? 2
                                  : 2 <= i.strategy || i.level < 2
                                    ? 4
                                    : 0,
                              ),
                              R(i, 255 & i.gzhead.os),
                              i.gzhead.extra &&
                                i.gzhead.extra.length &&
                                (R(i, 255 & i.gzhead.extra.length),
                                R(i, (i.gzhead.extra.length >> 8) & 255)),
                              i.gzhead.hcrc &&
                                (e.adler = a(
                                  e.adler,
                                  i.pending_buf,
                                  i.pending,
                                  0,
                                )),
                              (i.gzindex = 0),
                              (i.status = 69))
                            : (R(i, 0),
                              R(i, 0),
                              R(i, 0),
                              R(i, 0),
                              R(i, 0),
                              R(
                                i,
                                9 === i.level
                                  ? 2
                                  : 2 <= i.strategy || i.level < 2
                                    ? 4
                                    : 0,
                              ),
                              R(i, 3),
                              (i.status = C)));
                      else {
                        var h = (g + ((i.w_bits - 8) << 4)) << 8;
                        ((h |=
                          (2 <= i.strategy || i.level < 2
                            ? 0
                            : i.level < 6
                              ? 1
                              : 6 === i.level
                                ? 2
                                : 3) << 6),
                          0 !== i.strstart && (h |= 32),
                          (h += 31 - (h % 31)),
                          (i.status = C),
                          P(i, h),
                          0 !== i.strstart &&
                            (P(i, e.adler >>> 16), P(i, 65535 & e.adler)),
                          (e.adler = 1));
                      }
                    if (69 === i.status)
                      if (i.gzhead.extra) {
                        for (
                          s = i.pending;
                          i.gzindex < (65535 & i.gzhead.extra.length) &&
                          (i.pending !== i.pending_buf_size ||
                            (i.gzhead.hcrc &&
                              i.pending > s &&
                              (e.adler = a(
                                e.adler,
                                i.pending_buf,
                                i.pending - s,
                                s,
                              )),
                            L(e),
                            (s = i.pending),
                            i.pending !== i.pending_buf_size));

                        )
                          (R(i, 255 & i.gzhead.extra[i.gzindex]), i.gzindex++);
                        (i.gzhead.hcrc &&
                          i.pending > s &&
                          (e.adler = a(
                            e.adler,
                            i.pending_buf,
                            i.pending - s,
                            s,
                          )),
                          i.gzindex === i.gzhead.extra.length &&
                            ((i.gzindex = 0), (i.status = 73)));
                      } else i.status = 73;
                    if (73 === i.status)
                      if (i.gzhead.name) {
                        s = i.pending;
                        do {
                          if (
                            i.pending === i.pending_buf_size &&
                            (i.gzhead.hcrc &&
                              i.pending > s &&
                              (e.adler = a(
                                e.adler,
                                i.pending_buf,
                                i.pending - s,
                                s,
                              )),
                            L(e),
                            (s = i.pending),
                            i.pending === i.pending_buf_size)
                          ) {
                            d = 1;
                            break;
                          }
                          ((d =
                            i.gzindex < i.gzhead.name.length
                              ? 255 & i.gzhead.name.charCodeAt(i.gzindex++)
                              : 0),
                            R(i, d));
                        } while (0 !== d);
                        (i.gzhead.hcrc &&
                          i.pending > s &&
                          (e.adler = a(
                            e.adler,
                            i.pending_buf,
                            i.pending - s,
                            s,
                          )),
                          0 === d && ((i.gzindex = 0), (i.status = 91)));
                      } else i.status = 91;
                    if (91 === i.status)
                      if (i.gzhead.comment) {
                        s = i.pending;
                        do {
                          if (
                            i.pending === i.pending_buf_size &&
                            (i.gzhead.hcrc &&
                              i.pending > s &&
                              (e.adler = a(
                                e.adler,
                                i.pending_buf,
                                i.pending - s,
                                s,
                              )),
                            L(e),
                            (s = i.pending),
                            i.pending === i.pending_buf_size)
                          ) {
                            d = 1;
                            break;
                          }
                          ((d =
                            i.gzindex < i.gzhead.comment.length
                              ? 255 & i.gzhead.comment.charCodeAt(i.gzindex++)
                              : 0),
                            R(i, d));
                        } while (0 !== d);
                        (i.gzhead.hcrc &&
                          i.pending > s &&
                          (e.adler = a(
                            e.adler,
                            i.pending_buf,
                            i.pending - s,
                            s,
                          )),
                          0 === d && (i.status = 103));
                      } else i.status = 103;
                    if (
                      (103 === i.status &&
                        (i.gzhead.hcrc
                          ? (i.pending + 2 > i.pending_buf_size && L(e),
                            i.pending + 2 <= i.pending_buf_size &&
                              (R(i, 255 & e.adler),
                              R(i, (e.adler >> 8) & 255),
                              (e.adler = 0),
                              (i.status = C)))
                          : (i.status = C)),
                      0 !== i.pending)
                    ) {
                      if ((L(e), 0 === e.avail_out))
                        return ((i.last_flush = -1), u);
                    } else if (0 === e.avail_in && z(t) <= z(r) && t !== c)
                      return U(e, -5);
                    if (666 === i.status && 0 !== e.avail_in) return U(e, -5);
                    if (
                      0 !== e.avail_in ||
                      0 !== i.lookahead ||
                      (t !== l && 666 !== i.status)
                    ) {
                      var m =
                        2 === i.strategy
                          ? (function (e, t) {
                              for (var r; ; ) {
                                if (
                                  0 === e.lookahead &&
                                  (F(e), 0 === e.lookahead)
                                ) {
                                  if (t === l) return O;
                                  break;
                                }
                                if (
                                  ((e.match_length = 0),
                                  (r = o._tr_tally(e, 0, e.window[e.strstart])),
                                  e.lookahead--,
                                  e.strstart++,
                                  r && (M(e, !1), 0 === e.strm.avail_out))
                                )
                                  return O;
                              }
                              return (
                                (e.insert = 0),
                                t === c
                                  ? (M(e, !0), 0 === e.strm.avail_out ? D : T)
                                  : e.last_lit &&
                                      (M(e, !1), 0 === e.strm.avail_out)
                                    ? O
                                    : E
                              );
                            })(i, t)
                          : 3 === i.strategy
                            ? (function (e, t) {
                                for (var r, n, i, s, a = e.window; ; ) {
                                  if (e.lookahead <= k) {
                                    if ((F(e), e.lookahead <= k && t === l))
                                      return O;
                                    if (0 === e.lookahead) break;
                                  }
                                  if (
                                    ((e.match_length = 0),
                                    e.lookahead >= A &&
                                      0 < e.strstart &&
                                      (n = a[(i = e.strstart - 1)]) ===
                                        a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i])
                                  ) {
                                    s = e.strstart + k;
                                    do {} while (
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      i < s
                                    );
                                    ((e.match_length = k - (s - i)),
                                      e.match_length > e.lookahead &&
                                        (e.match_length = e.lookahead));
                                  }
                                  if (
                                    (e.match_length >= A
                                      ? ((r = o._tr_tally(
                                          e,
                                          1,
                                          e.match_length - A,
                                        )),
                                        (e.lookahead -= e.match_length),
                                        (e.strstart += e.match_length),
                                        (e.match_length = 0))
                                      : ((r = o._tr_tally(
                                          e,
                                          0,
                                          e.window[e.strstart],
                                        )),
                                        e.lookahead--,
                                        e.strstart++),
                                    r && (M(e, !1), 0 === e.strm.avail_out))
                                  )
                                    return O;
                                }
                                return (
                                  (e.insert = 0),
                                  t === c
                                    ? (M(e, !0), 0 === e.strm.avail_out ? D : T)
                                    : e.last_lit &&
                                        (M(e, !1), 0 === e.strm.avail_out)
                                      ? O
                                      : E
                                );
                              })(i, t)
                            : n[i.level].func(i, t);
                      if (
                        ((m !== D && m !== T) || (i.status = 666),
                        m === O || m === D)
                      )
                        return (0 === e.avail_out && (i.last_flush = -1), u);
                      if (
                        m === E &&
                        (1 === t
                          ? o._tr_align(i)
                          : 5 !== t &&
                            (o._tr_stored_block(i, 0, 0, !1),
                            3 === t &&
                              (B(i.head),
                              0 === i.lookahead &&
                                ((i.strstart = 0),
                                (i.block_start = 0),
                                (i.insert = 0)))),
                        L(e),
                        0 === e.avail_out)
                      )
                        return ((i.last_flush = -1), u);
                    }
                    return t !== c
                      ? u
                      : i.wrap <= 0
                        ? 1
                        : (2 === i.wrap
                            ? (R(i, 255 & e.adler),
                              R(i, (e.adler >> 8) & 255),
                              R(i, (e.adler >> 16) & 255),
                              R(i, (e.adler >> 24) & 255),
                              R(i, 255 & e.total_in),
                              R(i, (e.total_in >> 8) & 255),
                              R(i, (e.total_in >> 16) & 255),
                              R(i, (e.total_in >> 24) & 255))
                            : (P(i, e.adler >>> 16), P(i, 65535 & e.adler)),
                          L(e),
                          0 < i.wrap && (i.wrap = -i.wrap),
                          0 !== i.pending ? u : 1);
                  }),
                  (r.deflateEnd = function (e) {
                    var t;
                    return e && e.state
                      ? (t = e.state.status) !== I &&
                        69 !== t &&
                        73 !== t &&
                        91 !== t &&
                        103 !== t &&
                        t !== C &&
                        666 !== t
                        ? U(e, f)
                        : ((e.state = null), t === C ? U(e, -3) : u)
                      : f;
                  }),
                  (r.deflateSetDictionary = function (e, t) {
                    var r,
                      n,
                      o,
                      a,
                      d,
                      l,
                      c,
                      h,
                      m = t.length;
                    if (!e || !e.state) return f;
                    if (
                      2 === (a = (r = e.state).wrap) ||
                      (1 === a && r.status !== I) ||
                      r.lookahead
                    )
                      return f;
                    for (
                      1 === a && (e.adler = s(e.adler, t, m, 0)),
                        r.wrap = 0,
                        m >= r.w_size &&
                          (0 === a &&
                            (B(r.head),
                            (r.strstart = 0),
                            (r.block_start = 0),
                            (r.insert = 0)),
                          (h = new i.Buf8(r.w_size)),
                          i.arraySet(h, t, m - r.w_size, r.w_size, 0),
                          (t = h),
                          (m = r.w_size)),
                        d = e.avail_in,
                        l = e.next_in,
                        c = e.input,
                        e.avail_in = m,
                        e.next_in = 0,
                        e.input = t,
                        F(r);
                      r.lookahead >= A;

                    ) {
                      for (
                        n = r.strstart, o = r.lookahead - (A - 1);
                        (r.ins_h =
                          ((r.ins_h << r.hash_shift) ^ r.window[n + A - 1]) &
                          r.hash_mask),
                          (r.prev[n & r.w_mask] = r.head[r.ins_h]),
                          (r.head[r.ins_h] = n),
                          n++,
                          --o;

                      );
                      ((r.strstart = n), (r.lookahead = A - 1), F(r));
                    }
                    return (
                      (r.strstart += r.lookahead),
                      (r.block_start = r.strstart),
                      (r.insert = r.lookahead),
                      (r.lookahead = 0),
                      (r.match_length = r.prev_length = A - 1),
                      (r.match_available = 0),
                      (e.next_in = l),
                      (e.input = c),
                      (e.avail_in = d),
                      (r.wrap = a),
                      u
                    );
                  }),
                  (r.deflateInfo = "pako deflate (from Nodeca project)"));
              },
              {
                "../utils/common": 41,
                "./adler32": 43,
                "./crc32": 45,
                "./messages": 51,
                "./trees": 52,
              },
            ],
            47: [
              function (e, t, r) {
                "use strict";
                t.exports = function () {
                  ((this.text = 0),
                    (this.time = 0),
                    (this.xflags = 0),
                    (this.os = 0),
                    (this.extra = null),
                    (this.extra_len = 0),
                    (this.name = ""),
                    (this.comment = ""),
                    (this.hcrc = 0),
                    (this.done = !1));
                };
              },
              {},
            ],
            48: [
              function (e, t, r) {
                "use strict";
                t.exports = function (e, t) {
                  var r,
                    n,
                    i,
                    o,
                    s,
                    a,
                    d,
                    l,
                    c,
                    u,
                    f,
                    h,
                    m,
                    p,
                    g,
                    w,
                    y,
                    v,
                    b,
                    _,
                    x,
                    A,
                    k,
                    S,
                    I;
                  ((r = e.state),
                    (n = e.next_in),
                    (S = e.input),
                    (i = n + (e.avail_in - 5)),
                    (o = e.next_out),
                    (I = e.output),
                    (s = o - (t - e.avail_out)),
                    (a = o + (e.avail_out - 257)),
                    (d = r.dmax),
                    (l = r.wsize),
                    (c = r.whave),
                    (u = r.wnext),
                    (f = r.window),
                    (h = r.hold),
                    (m = r.bits),
                    (p = r.lencode),
                    (g = r.distcode),
                    (w = (1 << r.lenbits) - 1),
                    (y = (1 << r.distbits) - 1));
                  e: do {
                    (m < 15 &&
                      ((h += S[n++] << m),
                      (m += 8),
                      (h += S[n++] << m),
                      (m += 8)),
                      (v = p[h & w]));
                    t: for (;;) {
                      if (
                        ((h >>>= b = v >>> 24),
                        (m -= b),
                        0 == (b = (v >>> 16) & 255))
                      )
                        I[o++] = 65535 & v;
                      else {
                        if (!(16 & b)) {
                          if (!(64 & b)) {
                            v = p[(65535 & v) + (h & ((1 << b) - 1))];
                            continue t;
                          }
                          if (32 & b) {
                            r.mode = 12;
                            break e;
                          }
                          ((e.msg = "invalid literal/length code"),
                            (r.mode = 30));
                          break e;
                        }
                        ((_ = 65535 & v),
                          (b &= 15) &&
                            (m < b && ((h += S[n++] << m), (m += 8)),
                            (_ += h & ((1 << b) - 1)),
                            (h >>>= b),
                            (m -= b)),
                          m < 15 &&
                            ((h += S[n++] << m),
                            (m += 8),
                            (h += S[n++] << m),
                            (m += 8)),
                          (v = g[h & y]));
                        r: for (;;) {
                          if (
                            ((h >>>= b = v >>> 24),
                            (m -= b),
                            !(16 & (b = (v >>> 16) & 255)))
                          ) {
                            if (!(64 & b)) {
                              v = g[(65535 & v) + (h & ((1 << b) - 1))];
                              continue r;
                            }
                            ((e.msg = "invalid distance code"), (r.mode = 30));
                            break e;
                          }
                          if (
                            ((x = 65535 & v),
                            m < (b &= 15) &&
                              ((h += S[n++] << m),
                              (m += 8) < b && ((h += S[n++] << m), (m += 8))),
                            d < (x += h & ((1 << b) - 1)))
                          ) {
                            ((e.msg = "invalid distance too far back"),
                              (r.mode = 30));
                            break e;
                          }
                          if (((h >>>= b), (m -= b), (b = o - s) < x)) {
                            if (c < (b = x - b) && r.sane) {
                              ((e.msg = "invalid distance too far back"),
                                (r.mode = 30));
                              break e;
                            }
                            if (((k = f), (A = 0) === u)) {
                              if (((A += l - b), b < _)) {
                                for (_ -= b; (I[o++] = f[A++]), --b; );
                                ((A = o - x), (k = I));
                              }
                            } else if (u < b) {
                              if (((A += l + u - b), (b -= u) < _)) {
                                for (_ -= b; (I[o++] = f[A++]), --b; );
                                if (((A = 0), u < _)) {
                                  for (_ -= b = u; (I[o++] = f[A++]), --b; );
                                  ((A = o - x), (k = I));
                                }
                              }
                            } else if (((A += u - b), b < _)) {
                              for (_ -= b; (I[o++] = f[A++]), --b; );
                              ((A = o - x), (k = I));
                            }
                            for (; 2 < _; )
                              ((I[o++] = k[A++]),
                                (I[o++] = k[A++]),
                                (I[o++] = k[A++]),
                                (_ -= 3));
                            _ &&
                              ((I[o++] = k[A++]), 1 < _ && (I[o++] = k[A++]));
                          } else {
                            for (
                              A = o - x;
                              (I[o++] = I[A++]),
                                (I[o++] = I[A++]),
                                (I[o++] = I[A++]),
                                2 < (_ -= 3);

                            );
                            _ &&
                              ((I[o++] = I[A++]), 1 < _ && (I[o++] = I[A++]));
                          }
                          break;
                        }
                      }
                      break;
                    }
                  } while (n < i && o < a);
                  ((n -= _ = m >> 3),
                    (h &= (1 << (m -= _ << 3)) - 1),
                    (e.next_in = n),
                    (e.next_out = o),
                    (e.avail_in = n < i ? i - n + 5 : 5 - (n - i)),
                    (e.avail_out = o < a ? a - o + 257 : 257 - (o - a)),
                    (r.hold = h),
                    (r.bits = m));
                };
              },
              {},
            ],
            49: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils/common"),
                  i = e("./adler32"),
                  o = e("./crc32"),
                  s = e("./inffast"),
                  a = e("./inftrees"),
                  d = 1,
                  l = 2,
                  c = 0,
                  u = -2,
                  f = 1,
                  h = 852,
                  m = 592;
                function p(e) {
                  return (
                    ((e >>> 24) & 255) +
                    ((e >>> 8) & 65280) +
                    ((65280 & e) << 8) +
                    ((255 & e) << 24)
                  );
                }
                function g() {
                  ((this.mode = 0),
                    (this.last = !1),
                    (this.wrap = 0),
                    (this.havedict = !1),
                    (this.flags = 0),
                    (this.dmax = 0),
                    (this.check = 0),
                    (this.total = 0),
                    (this.head = null),
                    (this.wbits = 0),
                    (this.wsize = 0),
                    (this.whave = 0),
                    (this.wnext = 0),
                    (this.window = null),
                    (this.hold = 0),
                    (this.bits = 0),
                    (this.length = 0),
                    (this.offset = 0),
                    (this.extra = 0),
                    (this.lencode = null),
                    (this.distcode = null),
                    (this.lenbits = 0),
                    (this.distbits = 0),
                    (this.ncode = 0),
                    (this.nlen = 0),
                    (this.ndist = 0),
                    (this.have = 0),
                    (this.next = null),
                    (this.lens = new n.Buf16(320)),
                    (this.work = new n.Buf16(288)),
                    (this.lendyn = null),
                    (this.distdyn = null),
                    (this.sane = 0),
                    (this.back = 0),
                    (this.was = 0));
                }
                function w(e) {
                  var t;
                  return e && e.state
                    ? ((t = e.state),
                      (e.total_in = e.total_out = t.total = 0),
                      (e.msg = ""),
                      t.wrap && (e.adler = 1 & t.wrap),
                      (t.mode = f),
                      (t.last = 0),
                      (t.havedict = 0),
                      (t.dmax = 32768),
                      (t.head = null),
                      (t.hold = 0),
                      (t.bits = 0),
                      (t.lencode = t.lendyn = new n.Buf32(h)),
                      (t.distcode = t.distdyn = new n.Buf32(m)),
                      (t.sane = 1),
                      (t.back = -1),
                      c)
                    : u;
                }
                function y(e) {
                  var t;
                  return e && e.state
                    ? (((t = e.state).wsize = 0),
                      (t.whave = 0),
                      (t.wnext = 0),
                      w(e))
                    : u;
                }
                function v(e, t) {
                  var r, n;
                  return e && e.state
                    ? ((n = e.state),
                      t < 0
                        ? ((r = 0), (t = -t))
                        : ((r = 1 + (t >> 4)), t < 48 && (t &= 15)),
                      t && (t < 8 || 15 < t)
                        ? u
                        : (null !== n.window &&
                            n.wbits !== t &&
                            (n.window = null),
                          (n.wrap = r),
                          (n.wbits = t),
                          y(e)))
                    : u;
                }
                function b(e, t) {
                  var r, n;
                  return e
                    ? ((n = new g()),
                      ((e.state = n).window = null),
                      (r = v(e, t)) !== c && (e.state = null),
                      r)
                    : u;
                }
                var _,
                  x,
                  A = !0;
                function k(e) {
                  if (A) {
                    var t;
                    for (
                      _ = new n.Buf32(512), x = new n.Buf32(32), t = 0;
                      t < 144;

                    )
                      e.lens[t++] = 8;
                    for (; t < 256; ) e.lens[t++] = 9;
                    for (; t < 280; ) e.lens[t++] = 7;
                    for (; t < 288; ) e.lens[t++] = 8;
                    for (
                      a(d, e.lens, 0, 288, _, 0, e.work, { bits: 9 }), t = 0;
                      t < 32;

                    )
                      e.lens[t++] = 5;
                    (a(l, e.lens, 0, 32, x, 0, e.work, { bits: 5 }), (A = !1));
                  }
                  ((e.lencode = _),
                    (e.lenbits = 9),
                    (e.distcode = x),
                    (e.distbits = 5));
                }
                function S(e, t, r, i) {
                  var o,
                    s = e.state;
                  return (
                    null === s.window &&
                      ((s.wsize = 1 << s.wbits),
                      (s.wnext = 0),
                      (s.whave = 0),
                      (s.window = new n.Buf8(s.wsize))),
                    i >= s.wsize
                      ? (n.arraySet(s.window, t, r - s.wsize, s.wsize, 0),
                        (s.wnext = 0),
                        (s.whave = s.wsize))
                      : (i < (o = s.wsize - s.wnext) && (o = i),
                        n.arraySet(s.window, t, r - i, o, s.wnext),
                        (i -= o)
                          ? (n.arraySet(s.window, t, r - i, i, 0),
                            (s.wnext = i),
                            (s.whave = s.wsize))
                          : ((s.wnext += o),
                            s.wnext === s.wsize && (s.wnext = 0),
                            s.whave < s.wsize && (s.whave += o))),
                    0
                  );
                }
                ((r.inflateReset = y),
                  (r.inflateReset2 = v),
                  (r.inflateResetKeep = w),
                  (r.inflateInit = function (e) {
                    return b(e, 15);
                  }),
                  (r.inflateInit2 = b),
                  (r.inflate = function (e, t) {
                    var r,
                      h,
                      m,
                      g,
                      w,
                      y,
                      v,
                      b,
                      _,
                      x,
                      A,
                      I,
                      C,
                      O,
                      E,
                      D,
                      T,
                      U,
                      z,
                      B,
                      L,
                      M,
                      R,
                      P,
                      N = 0,
                      F = new n.Buf8(4),
                      j = [
                        16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2,
                        14, 1, 15,
                      ];
                    if (
                      !e ||
                      !e.state ||
                      !e.output ||
                      (!e.input && 0 !== e.avail_in)
                    )
                      return u;
                    (12 === (r = e.state).mode && (r.mode = 13),
                      (w = e.next_out),
                      (m = e.output),
                      (v = e.avail_out),
                      (g = e.next_in),
                      (h = e.input),
                      (y = e.avail_in),
                      (b = r.hold),
                      (_ = r.bits),
                      (x = y),
                      (A = v),
                      (M = c));
                    e: for (;;)
                      switch (r.mode) {
                        case f:
                          if (0 === r.wrap) {
                            r.mode = 13;
                            break;
                          }
                          for (; _ < 16; ) {
                            if (0 === y) break e;
                            (y--, (b += h[g++] << _), (_ += 8));
                          }
                          if (2 & r.wrap && 35615 === b) {
                            ((F[(r.check = 0)] = 255 & b),
                              (F[1] = (b >>> 8) & 255),
                              (r.check = o(r.check, F, 2, 0)),
                              (_ = b = 0),
                              (r.mode = 2));
                            break;
                          }
                          if (
                            ((r.flags = 0),
                            r.head && (r.head.done = !1),
                            !(1 & r.wrap) || (((255 & b) << 8) + (b >> 8)) % 31)
                          ) {
                            ((e.msg = "incorrect header check"), (r.mode = 30));
                            break;
                          }
                          if (8 != (15 & b)) {
                            ((e.msg = "unknown compression method"),
                              (r.mode = 30));
                            break;
                          }
                          if (
                            ((_ -= 4),
                            (L = 8 + (15 & (b >>>= 4))),
                            0 === r.wbits)
                          )
                            r.wbits = L;
                          else if (L > r.wbits) {
                            ((e.msg = "invalid window size"), (r.mode = 30));
                            break;
                          }
                          ((r.dmax = 1 << L),
                            (e.adler = r.check = 1),
                            (r.mode = 512 & b ? 10 : 12),
                            (_ = b = 0));
                          break;
                        case 2:
                          for (; _ < 16; ) {
                            if (0 === y) break e;
                            (y--, (b += h[g++] << _), (_ += 8));
                          }
                          if (((r.flags = b), 8 != (255 & r.flags))) {
                            ((e.msg = "unknown compression method"),
                              (r.mode = 30));
                            break;
                          }
                          if (57344 & r.flags) {
                            ((e.msg = "unknown header flags set"),
                              (r.mode = 30));
                            break;
                          }
                          (r.head && (r.head.text = (b >> 8) & 1),
                            512 & r.flags &&
                              ((F[0] = 255 & b),
                              (F[1] = (b >>> 8) & 255),
                              (r.check = o(r.check, F, 2, 0))),
                            (_ = b = 0),
                            (r.mode = 3));
                        case 3:
                          for (; _ < 32; ) {
                            if (0 === y) break e;
                            (y--, (b += h[g++] << _), (_ += 8));
                          }
                          (r.head && (r.head.time = b),
                            512 & r.flags &&
                              ((F[0] = 255 & b),
                              (F[1] = (b >>> 8) & 255),
                              (F[2] = (b >>> 16) & 255),
                              (F[3] = (b >>> 24) & 255),
                              (r.check = o(r.check, F, 4, 0))),
                            (_ = b = 0),
                            (r.mode = 4));
                        case 4:
                          for (; _ < 16; ) {
                            if (0 === y) break e;
                            (y--, (b += h[g++] << _), (_ += 8));
                          }
                          (r.head &&
                            ((r.head.xflags = 255 & b), (r.head.os = b >> 8)),
                            512 & r.flags &&
                              ((F[0] = 255 & b),
                              (F[1] = (b >>> 8) & 255),
                              (r.check = o(r.check, F, 2, 0))),
                            (_ = b = 0),
                            (r.mode = 5));
                        case 5:
                          if (1024 & r.flags) {
                            for (; _ < 16; ) {
                              if (0 === y) break e;
                              (y--, (b += h[g++] << _), (_ += 8));
                            }
                            ((r.length = b),
                              r.head && (r.head.extra_len = b),
                              512 & r.flags &&
                                ((F[0] = 255 & b),
                                (F[1] = (b >>> 8) & 255),
                                (r.check = o(r.check, F, 2, 0))),
                              (_ = b = 0));
                          } else r.head && (r.head.extra = null);
                          r.mode = 6;
                        case 6:
                          if (
                            1024 & r.flags &&
                            (y < (I = r.length) && (I = y),
                            I &&
                              (r.head &&
                                ((L = r.head.extra_len - r.length),
                                r.head.extra ||
                                  (r.head.extra = new Array(r.head.extra_len)),
                                n.arraySet(r.head.extra, h, g, I, L)),
                              512 & r.flags && (r.check = o(r.check, h, I, g)),
                              (y -= I),
                              (g += I),
                              (r.length -= I)),
                            r.length)
                          )
                            break e;
                          ((r.length = 0), (r.mode = 7));
                        case 7:
                          if (2048 & r.flags) {
                            if (0 === y) break e;
                            for (
                              I = 0;
                              (L = h[g + I++]),
                                r.head &&
                                  L &&
                                  r.length < 65536 &&
                                  (r.head.name += String.fromCharCode(L)),
                                L && I < y;

                            );
                            if (
                              (512 & r.flags && (r.check = o(r.check, h, I, g)),
                              (y -= I),
                              (g += I),
                              L)
                            )
                              break e;
                          } else r.head && (r.head.name = null);
                          ((r.length = 0), (r.mode = 8));
                        case 8:
                          if (4096 & r.flags) {
                            if (0 === y) break e;
                            for (
                              I = 0;
                              (L = h[g + I++]),
                                r.head &&
                                  L &&
                                  r.length < 65536 &&
                                  (r.head.comment += String.fromCharCode(L)),
                                L && I < y;

                            );
                            if (
                              (512 & r.flags && (r.check = o(r.check, h, I, g)),
                              (y -= I),
                              (g += I),
                              L)
                            )
                              break e;
                          } else r.head && (r.head.comment = null);
                          r.mode = 9;
                        case 9:
                          if (512 & r.flags) {
                            for (; _ < 16; ) {
                              if (0 === y) break e;
                              (y--, (b += h[g++] << _), (_ += 8));
                            }
                            if (b !== (65535 & r.check)) {
                              ((e.msg = "header crc mismatch"), (r.mode = 30));
                              break;
                            }
                            _ = b = 0;
                          }
                          (r.head &&
                            ((r.head.hcrc = (r.flags >> 9) & 1),
                            (r.head.done = !0)),
                            (e.adler = r.check = 0),
                            (r.mode = 12));
                          break;
                        case 10:
                          for (; _ < 32; ) {
                            if (0 === y) break e;
                            (y--, (b += h[g++] << _), (_ += 8));
                          }
                          ((e.adler = r.check = p(b)),
                            (_ = b = 0),
                            (r.mode = 11));
                        case 11:
                          if (0 === r.havedict)
                            return (
                              (e.next_out = w),
                              (e.avail_out = v),
                              (e.next_in = g),
                              (e.avail_in = y),
                              (r.hold = b),
                              (r.bits = _),
                              2
                            );
                          ((e.adler = r.check = 1), (r.mode = 12));
                        case 12:
                          if (5 === t || 6 === t) break e;
                        case 13:
                          if (r.last) {
                            ((b >>>= 7 & _), (_ -= 7 & _), (r.mode = 27));
                            break;
                          }
                          for (; _ < 3; ) {
                            if (0 === y) break e;
                            (y--, (b += h[g++] << _), (_ += 8));
                          }
                          switch (
                            ((r.last = 1 & b), (_ -= 1), 3 & (b >>>= 1))
                          ) {
                            case 0:
                              r.mode = 14;
                              break;
                            case 1:
                              if ((k(r), (r.mode = 20), 6 !== t)) break;
                              ((b >>>= 2), (_ -= 2));
                              break e;
                            case 2:
                              r.mode = 17;
                              break;
                            case 3:
                              ((e.msg = "invalid block type"), (r.mode = 30));
                          }
                          ((b >>>= 2), (_ -= 2));
                          break;
                        case 14:
                          for (b >>>= 7 & _, _ -= 7 & _; _ < 32; ) {
                            if (0 === y) break e;
                            (y--, (b += h[g++] << _), (_ += 8));
                          }
                          if ((65535 & b) != ((b >>> 16) ^ 65535)) {
                            ((e.msg = "invalid stored block lengths"),
                              (r.mode = 30));
                            break;
                          }
                          if (
                            ((r.length = 65535 & b),
                            (_ = b = 0),
                            (r.mode = 15),
                            6 === t)
                          )
                            break e;
                        case 15:
                          r.mode = 16;
                        case 16:
                          if ((I = r.length)) {
                            if ((y < I && (I = y), v < I && (I = v), 0 === I))
                              break e;
                            (n.arraySet(m, h, g, I, w),
                              (y -= I),
                              (g += I),
                              (v -= I),
                              (w += I),
                              (r.length -= I));
                            break;
                          }
                          r.mode = 12;
                          break;
                        case 17:
                          for (; _ < 14; ) {
                            if (0 === y) break e;
                            (y--, (b += h[g++] << _), (_ += 8));
                          }
                          if (
                            ((r.nlen = 257 + (31 & b)),
                            (b >>>= 5),
                            (_ -= 5),
                            (r.ndist = 1 + (31 & b)),
                            (b >>>= 5),
                            (_ -= 5),
                            (r.ncode = 4 + (15 & b)),
                            (b >>>= 4),
                            (_ -= 4),
                            286 < r.nlen || 30 < r.ndist)
                          ) {
                            ((e.msg = "too many length or distance symbols"),
                              (r.mode = 30));
                            break;
                          }
                          ((r.have = 0), (r.mode = 18));
                        case 18:
                          for (; r.have < r.ncode; ) {
                            for (; _ < 3; ) {
                              if (0 === y) break e;
                              (y--, (b += h[g++] << _), (_ += 8));
                            }
                            ((r.lens[j[r.have++]] = 7 & b),
                              (b >>>= 3),
                              (_ -= 3));
                          }
                          for (; r.have < 19; ) r.lens[j[r.have++]] = 0;
                          if (
                            ((r.lencode = r.lendyn),
                            (r.lenbits = 7),
                            (R = { bits: r.lenbits }),
                            (M = a(0, r.lens, 0, 19, r.lencode, 0, r.work, R)),
                            (r.lenbits = R.bits),
                            M)
                          ) {
                            ((e.msg = "invalid code lengths set"),
                              (r.mode = 30));
                            break;
                          }
                          ((r.have = 0), (r.mode = 19));
                        case 19:
                          for (; r.have < r.nlen + r.ndist; ) {
                            for (
                              ;
                              (D =
                                ((N = r.lencode[b & ((1 << r.lenbits) - 1)]) >>>
                                  16) &
                                255),
                                (T = 65535 & N),
                                !((E = N >>> 24) <= _);

                            ) {
                              if (0 === y) break e;
                              (y--, (b += h[g++] << _), (_ += 8));
                            }
                            if (T < 16)
                              ((b >>>= E), (_ -= E), (r.lens[r.have++] = T));
                            else {
                              if (16 === T) {
                                for (P = E + 2; _ < P; ) {
                                  if (0 === y) break e;
                                  (y--, (b += h[g++] << _), (_ += 8));
                                }
                                if (((b >>>= E), (_ -= E), 0 === r.have)) {
                                  ((e.msg = "invalid bit length repeat"),
                                    (r.mode = 30));
                                  break;
                                }
                                ((L = r.lens[r.have - 1]),
                                  (I = 3 + (3 & b)),
                                  (b >>>= 2),
                                  (_ -= 2));
                              } else if (17 === T) {
                                for (P = E + 3; _ < P; ) {
                                  if (0 === y) break e;
                                  (y--, (b += h[g++] << _), (_ += 8));
                                }
                                ((_ -= E),
                                  (L = 0),
                                  (I = 3 + (7 & (b >>>= E))),
                                  (b >>>= 3),
                                  (_ -= 3));
                              } else {
                                for (P = E + 7; _ < P; ) {
                                  if (0 === y) break e;
                                  (y--, (b += h[g++] << _), (_ += 8));
                                }
                                ((_ -= E),
                                  (L = 0),
                                  (I = 11 + (127 & (b >>>= E))),
                                  (b >>>= 7),
                                  (_ -= 7));
                              }
                              if (r.have + I > r.nlen + r.ndist) {
                                ((e.msg = "invalid bit length repeat"),
                                  (r.mode = 30));
                                break;
                              }
                              for (; I--; ) r.lens[r.have++] = L;
                            }
                          }
                          if (30 === r.mode) break;
                          if (0 === r.lens[256]) {
                            ((e.msg = "invalid code -- missing end-of-block"),
                              (r.mode = 30));
                            break;
                          }
                          if (
                            ((r.lenbits = 9),
                            (R = { bits: r.lenbits }),
                            (M = a(
                              d,
                              r.lens,
                              0,
                              r.nlen,
                              r.lencode,
                              0,
                              r.work,
                              R,
                            )),
                            (r.lenbits = R.bits),
                            M)
                          ) {
                            ((e.msg = "invalid literal/lengths set"),
                              (r.mode = 30));
                            break;
                          }
                          if (
                            ((r.distbits = 6),
                            (r.distcode = r.distdyn),
                            (R = { bits: r.distbits }),
                            (M = a(
                              l,
                              r.lens,
                              r.nlen,
                              r.ndist,
                              r.distcode,
                              0,
                              r.work,
                              R,
                            )),
                            (r.distbits = R.bits),
                            M)
                          ) {
                            ((e.msg = "invalid distances set"), (r.mode = 30));
                            break;
                          }
                          if (((r.mode = 20), 6 === t)) break e;
                        case 20:
                          r.mode = 21;
                        case 21:
                          if (6 <= y && 258 <= v) {
                            ((e.next_out = w),
                              (e.avail_out = v),
                              (e.next_in = g),
                              (e.avail_in = y),
                              (r.hold = b),
                              (r.bits = _),
                              s(e, A),
                              (w = e.next_out),
                              (m = e.output),
                              (v = e.avail_out),
                              (g = e.next_in),
                              (h = e.input),
                              (y = e.avail_in),
                              (b = r.hold),
                              (_ = r.bits),
                              12 === r.mode && (r.back = -1));
                            break;
                          }
                          for (
                            r.back = 0;
                            (D =
                              ((N = r.lencode[b & ((1 << r.lenbits) - 1)]) >>>
                                16) &
                              255),
                              (T = 65535 & N),
                              !((E = N >>> 24) <= _);

                          ) {
                            if (0 === y) break e;
                            (y--, (b += h[g++] << _), (_ += 8));
                          }
                          if (D && !(240 & D)) {
                            for (
                              U = E, z = D, B = T;
                              (D =
                                ((N =
                                  r.lencode[
                                    B + ((b & ((1 << (U + z)) - 1)) >> U)
                                  ]) >>>
                                  16) &
                                255),
                                (T = 65535 & N),
                                !(U + (E = N >>> 24) <= _);

                            ) {
                              if (0 === y) break e;
                              (y--, (b += h[g++] << _), (_ += 8));
                            }
                            ((b >>>= U), (_ -= U), (r.back += U));
                          }
                          if (
                            ((b >>>= E),
                            (_ -= E),
                            (r.back += E),
                            (r.length = T),
                            0 === D)
                          ) {
                            r.mode = 26;
                            break;
                          }
                          if (32 & D) {
                            ((r.back = -1), (r.mode = 12));
                            break;
                          }
                          if (64 & D) {
                            ((e.msg = "invalid literal/length code"),
                              (r.mode = 30));
                            break;
                          }
                          ((r.extra = 15 & D), (r.mode = 22));
                        case 22:
                          if (r.extra) {
                            for (P = r.extra; _ < P; ) {
                              if (0 === y) break e;
                              (y--, (b += h[g++] << _), (_ += 8));
                            }
                            ((r.length += b & ((1 << r.extra) - 1)),
                              (b >>>= r.extra),
                              (_ -= r.extra),
                              (r.back += r.extra));
                          }
                          ((r.was = r.length), (r.mode = 23));
                        case 23:
                          for (
                            ;
                            (D =
                              ((N = r.distcode[b & ((1 << r.distbits) - 1)]) >>>
                                16) &
                              255),
                              (T = 65535 & N),
                              !((E = N >>> 24) <= _);

                          ) {
                            if (0 === y) break e;
                            (y--, (b += h[g++] << _), (_ += 8));
                          }
                          if (!(240 & D)) {
                            for (
                              U = E, z = D, B = T;
                              (D =
                                ((N =
                                  r.distcode[
                                    B + ((b & ((1 << (U + z)) - 1)) >> U)
                                  ]) >>>
                                  16) &
                                255),
                                (T = 65535 & N),
                                !(U + (E = N >>> 24) <= _);

                            ) {
                              if (0 === y) break e;
                              (y--, (b += h[g++] << _), (_ += 8));
                            }
                            ((b >>>= U), (_ -= U), (r.back += U));
                          }
                          if (((b >>>= E), (_ -= E), (r.back += E), 64 & D)) {
                            ((e.msg = "invalid distance code"), (r.mode = 30));
                            break;
                          }
                          ((r.offset = T), (r.extra = 15 & D), (r.mode = 24));
                        case 24:
                          if (r.extra) {
                            for (P = r.extra; _ < P; ) {
                              if (0 === y) break e;
                              (y--, (b += h[g++] << _), (_ += 8));
                            }
                            ((r.offset += b & ((1 << r.extra) - 1)),
                              (b >>>= r.extra),
                              (_ -= r.extra),
                              (r.back += r.extra));
                          }
                          if (r.offset > r.dmax) {
                            ((e.msg = "invalid distance too far back"),
                              (r.mode = 30));
                            break;
                          }
                          r.mode = 25;
                        case 25:
                          if (0 === v) break e;
                          if (((I = A - v), r.offset > I)) {
                            if ((I = r.offset - I) > r.whave && r.sane) {
                              ((e.msg = "invalid distance too far back"),
                                (r.mode = 30));
                              break;
                            }
                            ((C =
                              I > r.wnext
                                ? ((I -= r.wnext), r.wsize - I)
                                : r.wnext - I),
                              I > r.length && (I = r.length),
                              (O = r.window));
                          } else ((O = m), (C = w - r.offset), (I = r.length));
                          for (
                            v < I && (I = v), v -= I, r.length -= I;
                            (m[w++] = O[C++]), --I;

                          );
                          0 === r.length && (r.mode = 21);
                          break;
                        case 26:
                          if (0 === v) break e;
                          ((m[w++] = r.length), v--, (r.mode = 21));
                          break;
                        case 27:
                          if (r.wrap) {
                            for (; _ < 32; ) {
                              if (0 === y) break e;
                              (y--, (b |= h[g++] << _), (_ += 8));
                            }
                            if (
                              ((A -= v),
                              (e.total_out += A),
                              (r.total += A),
                              A &&
                                (e.adler = r.check =
                                  r.flags
                                    ? o(r.check, m, A, w - A)
                                    : i(r.check, m, A, w - A)),
                              (A = v),
                              (r.flags ? b : p(b)) !== r.check)
                            ) {
                              ((e.msg = "incorrect data check"), (r.mode = 30));
                              break;
                            }
                            _ = b = 0;
                          }
                          r.mode = 28;
                        case 28:
                          if (r.wrap && r.flags) {
                            for (; _ < 32; ) {
                              if (0 === y) break e;
                              (y--, (b += h[g++] << _), (_ += 8));
                            }
                            if (b !== (4294967295 & r.total)) {
                              ((e.msg = "incorrect length check"),
                                (r.mode = 30));
                              break;
                            }
                            _ = b = 0;
                          }
                          r.mode = 29;
                        case 29:
                          M = 1;
                          break e;
                        case 30:
                          M = -3;
                          break e;
                        case 31:
                          return -4;
                        default:
                          return u;
                      }
                    return (
                      (e.next_out = w),
                      (e.avail_out = v),
                      (e.next_in = g),
                      (e.avail_in = y),
                      (r.hold = b),
                      (r.bits = _),
                      (r.wsize ||
                        (A !== e.avail_out &&
                          r.mode < 30 &&
                          (r.mode < 27 || 4 !== t))) &&
                      S(e, e.output, e.next_out, A - e.avail_out)
                        ? ((r.mode = 31), -4)
                        : ((x -= e.avail_in),
                          (A -= e.avail_out),
                          (e.total_in += x),
                          (e.total_out += A),
                          (r.total += A),
                          r.wrap &&
                            A &&
                            (e.adler = r.check =
                              r.flags
                                ? o(r.check, m, A, e.next_out - A)
                                : i(r.check, m, A, e.next_out - A)),
                          (e.data_type =
                            r.bits +
                            (r.last ? 64 : 0) +
                            (12 === r.mode ? 128 : 0) +
                            (20 === r.mode || 15 === r.mode ? 256 : 0)),
                          ((0 == x && 0 === A) || 4 === t) &&
                            M === c &&
                            (M = -5),
                          M)
                    );
                  }),
                  (r.inflateEnd = function (e) {
                    if (!e || !e.state) return u;
                    var t = e.state;
                    return (t.window && (t.window = null), (e.state = null), c);
                  }),
                  (r.inflateGetHeader = function (e, t) {
                    var r;
                    return e && e.state && 2 & (r = e.state).wrap
                      ? (((r.head = t).done = !1), c)
                      : u;
                  }),
                  (r.inflateSetDictionary = function (e, t) {
                    var r,
                      n = t.length;
                    return e && e.state
                      ? 0 !== (r = e.state).wrap && 11 !== r.mode
                        ? u
                        : 11 === r.mode && i(1, t, n, 0) !== r.check
                          ? -3
                          : S(e, t, n, n)
                            ? ((r.mode = 31), -4)
                            : ((r.havedict = 1), c)
                      : u;
                  }),
                  (r.inflateInfo = "pako inflate (from Nodeca project)"));
              },
              {
                "../utils/common": 41,
                "./adler32": 43,
                "./crc32": 45,
                "./inffast": 48,
                "./inftrees": 50,
              },
            ],
            50: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils/common"),
                  i = [
                    3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31, 35,
                    43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 0, 0,
                  ],
                  o = [
                    16, 16, 16, 16, 16, 16, 16, 16, 17, 17, 17, 17, 18, 18, 18,
                    18, 19, 19, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 16, 72,
                    78,
                  ],
                  s = [
                    1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193,
                    257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145,
                    8193, 12289, 16385, 24577, 0, 0,
                  ],
                  a = [
                    16, 16, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22,
                    22, 23, 23, 24, 24, 25, 25, 26, 26, 27, 27, 28, 28, 29, 29,
                    64, 64,
                  ];
                t.exports = function (e, t, r, d, l, c, u, f) {
                  var h,
                    m,
                    p,
                    g,
                    w,
                    y,
                    v,
                    b,
                    _,
                    x = f.bits,
                    A = 0,
                    k = 0,
                    S = 0,
                    I = 0,
                    C = 0,
                    O = 0,
                    E = 0,
                    D = 0,
                    T = 0,
                    U = 0,
                    z = null,
                    B = 0,
                    L = new n.Buf16(16),
                    M = new n.Buf16(16),
                    R = null,
                    P = 0;
                  for (A = 0; A <= 15; A++) L[A] = 0;
                  for (k = 0; k < d; k++) L[t[r + k]]++;
                  for (C = x, I = 15; 1 <= I && 0 === L[I]; I--);
                  if ((I < C && (C = I), 0 === I))
                    return (
                      (l[c++] = 20971520),
                      (l[c++] = 20971520),
                      (f.bits = 1),
                      0
                    );
                  for (S = 1; S < I && 0 === L[S]; S++);
                  for (C < S && (C = S), A = D = 1; A <= 15; A++)
                    if (((D <<= 1), (D -= L[A]) < 0)) return -1;
                  if (0 < D && (0 === e || 1 !== I)) return -1;
                  for (M[1] = 0, A = 1; A < 15; A++) M[A + 1] = M[A] + L[A];
                  for (k = 0; k < d; k++)
                    0 !== t[r + k] && (u[M[t[r + k]]++] = k);
                  if (
                    ((y =
                      0 === e
                        ? ((z = R = u), 19)
                        : 1 === e
                          ? ((z = i), (B -= 257), (R = o), (P -= 257), 256)
                          : ((z = s), (R = a), -1)),
                    (A = S),
                    (w = c),
                    (E = k = U = 0),
                    (p = -1),
                    (g = (T = 1 << (O = C)) - 1),
                    (1 === e && 852 < T) || (2 === e && 592 < T))
                  )
                    return 1;
                  for (;;) {
                    for (
                      v = A - E,
                        _ =
                          u[k] < y
                            ? ((b = 0), u[k])
                            : u[k] > y
                              ? ((b = R[P + u[k]]), z[B + u[k]])
                              : ((b = 96), 0),
                        h = 1 << (A - E),
                        S = m = 1 << O;
                      (l[w + (U >> E) + (m -= h)] = (v << 24) | (b << 16) | _),
                        0 !== m;

                    );
                    for (h = 1 << (A - 1); U & h; ) h >>= 1;
                    if (
                      (0 !== h ? ((U &= h - 1), (U += h)) : (U = 0),
                      k++,
                      0 == --L[A])
                    ) {
                      if (A === I) break;
                      A = t[r + u[k]];
                    }
                    if (C < A && (U & g) !== p) {
                      for (
                        0 === E && (E = C), w += S, D = 1 << (O = A - E);
                        O + E < I && !((D -= L[O + E]) <= 0);

                      )
                        (O++, (D <<= 1));
                      if (
                        ((T += 1 << O),
                        (1 === e && 852 < T) || (2 === e && 592 < T))
                      )
                        return 1;
                      l[(p = U & g)] = (C << 24) | (O << 16) | (w - c);
                    }
                  }
                  return (
                    0 !== U && (l[w + U] = ((A - E) << 24) | (64 << 16)),
                    (f.bits = C),
                    0
                  );
                };
              },
              { "../utils/common": 41 },
            ],
            51: [
              function (e, t, r) {
                "use strict";
                t.exports = {
                  2: "need dictionary",
                  1: "stream end",
                  0: "",
                  "-1": "file error",
                  "-2": "stream error",
                  "-3": "data error",
                  "-4": "insufficient memory",
                  "-5": "buffer error",
                  "-6": "incompatible version",
                };
              },
              {},
            ],
            52: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils/common"),
                  i = 0,
                  o = 1;
                function s(e) {
                  for (var t = e.length; 0 <= --t; ) e[t] = 0;
                }
                var a = 0,
                  d = 29,
                  l = 256,
                  c = l + 1 + d,
                  u = 30,
                  f = 19,
                  h = 2 * c + 1,
                  m = 15,
                  p = 16,
                  g = 7,
                  w = 256,
                  y = 16,
                  v = 17,
                  b = 18,
                  _ = [
                    0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3,
                    4, 4, 4, 4, 5, 5, 5, 5, 0,
                  ],
                  x = [
                    0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8,
                    9, 9, 10, 10, 11, 11, 12, 12, 13, 13,
                  ],
                  A = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7],
                  k = [
                    16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14,
                    1, 15,
                  ],
                  S = new Array(2 * (c + 2));
                s(S);
                var I = new Array(2 * u);
                s(I);
                var C = new Array(512);
                s(C);
                var O = new Array(256);
                s(O);
                var E = new Array(d);
                s(E);
                var D,
                  T,
                  U,
                  z = new Array(u);
                function B(e, t, r, n, i) {
                  ((this.static_tree = e),
                    (this.extra_bits = t),
                    (this.extra_base = r),
                    (this.elems = n),
                    (this.max_length = i),
                    (this.has_stree = e && e.length));
                }
                function L(e, t) {
                  ((this.dyn_tree = e),
                    (this.max_code = 0),
                    (this.stat_desc = t));
                }
                function M(e) {
                  return e < 256 ? C[e] : C[256 + (e >>> 7)];
                }
                function R(e, t) {
                  ((e.pending_buf[e.pending++] = 255 & t),
                    (e.pending_buf[e.pending++] = (t >>> 8) & 255));
                }
                function P(e, t, r) {
                  e.bi_valid > p - r
                    ? ((e.bi_buf |= (t << e.bi_valid) & 65535),
                      R(e, e.bi_buf),
                      (e.bi_buf = t >> (p - e.bi_valid)),
                      (e.bi_valid += r - p))
                    : ((e.bi_buf |= (t << e.bi_valid) & 65535),
                      (e.bi_valid += r));
                }
                function N(e, t, r) {
                  P(e, r[2 * t], r[2 * t + 1]);
                }
                function F(e, t) {
                  for (
                    var r = 0;
                    (r |= 1 & e), (e >>>= 1), (r <<= 1), 0 < --t;

                  );
                  return r >>> 1;
                }
                function j(e, t, r) {
                  var n,
                    i,
                    o = new Array(m + 1),
                    s = 0;
                  for (n = 1; n <= m; n++) o[n] = s = (s + r[n - 1]) << 1;
                  for (i = 0; i <= t; i++) {
                    var a = e[2 * i + 1];
                    0 !== a && (e[2 * i] = F(o[a]++, a));
                  }
                }
                function W(e) {
                  var t;
                  for (t = 0; t < c; t++) e.dyn_ltree[2 * t] = 0;
                  for (t = 0; t < u; t++) e.dyn_dtree[2 * t] = 0;
                  for (t = 0; t < f; t++) e.bl_tree[2 * t] = 0;
                  ((e.dyn_ltree[2 * w] = 1),
                    (e.opt_len = e.static_len = 0),
                    (e.last_lit = e.matches = 0));
                }
                function Z(e) {
                  (8 < e.bi_valid
                    ? R(e, e.bi_buf)
                    : 0 < e.bi_valid && (e.pending_buf[e.pending++] = e.bi_buf),
                    (e.bi_buf = 0),
                    (e.bi_valid = 0));
                }
                function $(e, t, r, n) {
                  var i = 2 * t,
                    o = 2 * r;
                  return e[i] < e[o] || (e[i] === e[o] && n[t] <= n[r]);
                }
                function V(e, t, r) {
                  for (
                    var n = e.heap[r], i = r << 1;
                    i <= e.heap_len &&
                    (i < e.heap_len &&
                      $(t, e.heap[i + 1], e.heap[i], e.depth) &&
                      i++,
                    !$(t, n, e.heap[i], e.depth));

                  )
                    ((e.heap[r] = e.heap[i]), (r = i), (i <<= 1));
                  e.heap[r] = n;
                }
                function K(e, t, r) {
                  var n,
                    i,
                    o,
                    s,
                    a = 0;
                  if (0 !== e.last_lit)
                    for (
                      ;
                      (n =
                        (e.pending_buf[e.d_buf + 2 * a] << 8) |
                        e.pending_buf[e.d_buf + 2 * a + 1]),
                        (i = e.pending_buf[e.l_buf + a]),
                        a++,
                        0 === n
                          ? N(e, i, t)
                          : (N(e, (o = O[i]) + l + 1, t),
                            0 !== (s = _[o]) && P(e, (i -= E[o]), s),
                            N(e, (o = M(--n)), r),
                            0 !== (s = x[o]) && P(e, (n -= z[o]), s)),
                        a < e.last_lit;

                    );
                  N(e, w, t);
                }
                function H(e, t) {
                  var r,
                    n,
                    i,
                    o = t.dyn_tree,
                    s = t.stat_desc.static_tree,
                    a = t.stat_desc.has_stree,
                    d = t.stat_desc.elems,
                    l = -1;
                  for (e.heap_len = 0, e.heap_max = h, r = 0; r < d; r++)
                    0 !== o[2 * r]
                      ? ((e.heap[++e.heap_len] = l = r), (e.depth[r] = 0))
                      : (o[2 * r + 1] = 0);
                  for (; e.heap_len < 2; )
                    ((o[2 * (i = e.heap[++e.heap_len] = l < 2 ? ++l : 0)] = 1),
                      (e.depth[i] = 0),
                      e.opt_len--,
                      a && (e.static_len -= s[2 * i + 1]));
                  for (t.max_code = l, r = e.heap_len >> 1; 1 <= r; r--)
                    V(e, o, r);
                  for (
                    i = d;
                    (r = e.heap[1]),
                      (e.heap[1] = e.heap[e.heap_len--]),
                      V(e, o, 1),
                      (n = e.heap[1]),
                      (e.heap[--e.heap_max] = r),
                      (e.heap[--e.heap_max] = n),
                      (o[2 * i] = o[2 * r] + o[2 * n]),
                      (e.depth[i] =
                        (e.depth[r] >= e.depth[n] ? e.depth[r] : e.depth[n]) +
                        1),
                      (o[2 * r + 1] = o[2 * n + 1] = i),
                      (e.heap[1] = i++),
                      V(e, o, 1),
                      2 <= e.heap_len;

                  );
                  ((e.heap[--e.heap_max] = e.heap[1]),
                    (function (e, t) {
                      var r,
                        n,
                        i,
                        o,
                        s,
                        a,
                        d = t.dyn_tree,
                        l = t.max_code,
                        c = t.stat_desc.static_tree,
                        u = t.stat_desc.has_stree,
                        f = t.stat_desc.extra_bits,
                        p = t.stat_desc.extra_base,
                        g = t.stat_desc.max_length,
                        w = 0;
                      for (o = 0; o <= m; o++) e.bl_count[o] = 0;
                      for (
                        d[2 * e.heap[e.heap_max] + 1] = 0, r = e.heap_max + 1;
                        r < h;
                        r++
                      )
                        (g < (o = d[2 * d[2 * (n = e.heap[r]) + 1] + 1] + 1) &&
                          ((o = g), w++),
                          (d[2 * n + 1] = o),
                          l < n ||
                            (e.bl_count[o]++,
                            (s = 0),
                            p <= n && (s = f[n - p]),
                            (a = d[2 * n]),
                            (e.opt_len += a * (o + s)),
                            u && (e.static_len += a * (c[2 * n + 1] + s))));
                      if (0 !== w) {
                        do {
                          for (o = g - 1; 0 === e.bl_count[o]; ) o--;
                          (e.bl_count[o]--,
                            (e.bl_count[o + 1] += 2),
                            e.bl_count[g]--,
                            (w -= 2));
                        } while (0 < w);
                        for (o = g; 0 !== o; o--)
                          for (n = e.bl_count[o]; 0 !== n; )
                            l < (i = e.heap[--r]) ||
                              (d[2 * i + 1] !== o &&
                                ((e.opt_len += (o - d[2 * i + 1]) * d[2 * i]),
                                (d[2 * i + 1] = o)),
                              n--);
                      }
                    })(e, t),
                    j(o, l, e.bl_count));
                }
                function G(e, t, r) {
                  var n,
                    i,
                    o = -1,
                    s = t[1],
                    a = 0,
                    d = 7,
                    l = 4;
                  for (
                    0 === s && ((d = 138), (l = 3)),
                      t[2 * (r + 1) + 1] = 65535,
                      n = 0;
                    n <= r;
                    n++
                  )
                    ((i = s),
                      (s = t[2 * (n + 1) + 1]),
                      (++a < d && i === s) ||
                        (a < l
                          ? (e.bl_tree[2 * i] += a)
                          : 0 !== i
                            ? (i !== o && e.bl_tree[2 * i]++,
                              e.bl_tree[2 * y]++)
                            : a <= 10
                              ? e.bl_tree[2 * v]++
                              : e.bl_tree[2 * b]++,
                        (o = i),
                        (l =
                          (a = 0) === s
                            ? ((d = 138), 3)
                            : i === s
                              ? ((d = 6), 3)
                              : ((d = 7), 4))));
                }
                function q(e, t, r) {
                  var n,
                    i,
                    o = -1,
                    s = t[1],
                    a = 0,
                    d = 7,
                    l = 4;
                  for (0 === s && ((d = 138), (l = 3)), n = 0; n <= r; n++)
                    if (
                      ((i = s), (s = t[2 * (n + 1) + 1]), !(++a < d && i === s))
                    ) {
                      if (a < l) for (; N(e, i, e.bl_tree), 0 != --a; );
                      else
                        0 !== i
                          ? (i !== o && (N(e, i, e.bl_tree), a--),
                            N(e, y, e.bl_tree),
                            P(e, a - 3, 2))
                          : a <= 10
                            ? (N(e, v, e.bl_tree), P(e, a - 3, 3))
                            : (N(e, b, e.bl_tree), P(e, a - 11, 7));
                      ((o = i),
                        (l =
                          (a = 0) === s
                            ? ((d = 138), 3)
                            : i === s
                              ? ((d = 6), 3)
                              : ((d = 7), 4)));
                    }
                }
                s(z);
                var Q = !1;
                function Y(e, t, r, i) {
                  (P(e, (a << 1) + (i ? 1 : 0), 3),
                    (function (e, t, r, i) {
                      (Z(e),
                        i && (R(e, r), R(e, ~r)),
                        n.arraySet(e.pending_buf, e.window, t, r, e.pending),
                        (e.pending += r));
                    })(e, t, r, !0));
                }
                ((r._tr_init = function (e) {
                  (Q ||
                    ((function () {
                      var e,
                        t,
                        r,
                        n,
                        i,
                        o = new Array(m + 1);
                      for (n = r = 0; n < d - 1; n++)
                        for (E[n] = r, e = 0; e < 1 << _[n]; e++) O[r++] = n;
                      for (O[r - 1] = n, n = i = 0; n < 16; n++)
                        for (z[n] = i, e = 0; e < 1 << x[n]; e++) C[i++] = n;
                      for (i >>= 7; n < u; n++)
                        for (z[n] = i << 7, e = 0; e < 1 << (x[n] - 7); e++)
                          C[256 + i++] = n;
                      for (t = 0; t <= m; t++) o[t] = 0;
                      for (e = 0; e <= 143; ) ((S[2 * e + 1] = 8), e++, o[8]++);
                      for (; e <= 255; ) ((S[2 * e + 1] = 9), e++, o[9]++);
                      for (; e <= 279; ) ((S[2 * e + 1] = 7), e++, o[7]++);
                      for (; e <= 287; ) ((S[2 * e + 1] = 8), e++, o[8]++);
                      for (j(S, c + 1, o), e = 0; e < u; e++)
                        ((I[2 * e + 1] = 5), (I[2 * e] = F(e, 5)));
                      ((D = new B(S, _, l + 1, c, m)),
                        (T = new B(I, x, 0, u, m)),
                        (U = new B(new Array(0), A, 0, f, g)));
                    })(),
                    (Q = !0)),
                    (e.l_desc = new L(e.dyn_ltree, D)),
                    (e.d_desc = new L(e.dyn_dtree, T)),
                    (e.bl_desc = new L(e.bl_tree, U)),
                    (e.bi_buf = 0),
                    (e.bi_valid = 0),
                    W(e));
                }),
                  (r._tr_stored_block = Y),
                  (r._tr_flush_block = function (e, t, r, n) {
                    var s,
                      a,
                      d = 0;
                    (0 < e.level
                      ? (2 === e.strm.data_type &&
                          (e.strm.data_type = (function (e) {
                            var t,
                              r = 4093624447;
                            for (t = 0; t <= 31; t++, r >>>= 1)
                              if (1 & r && 0 !== e.dyn_ltree[2 * t]) return i;
                            if (
                              0 !== e.dyn_ltree[18] ||
                              0 !== e.dyn_ltree[20] ||
                              0 !== e.dyn_ltree[26]
                            )
                              return o;
                            for (t = 32; t < l; t++)
                              if (0 !== e.dyn_ltree[2 * t]) return o;
                            return i;
                          })(e)),
                        H(e, e.l_desc),
                        H(e, e.d_desc),
                        (d = (function (e) {
                          var t;
                          for (
                            G(e, e.dyn_ltree, e.l_desc.max_code),
                              G(e, e.dyn_dtree, e.d_desc.max_code),
                              H(e, e.bl_desc),
                              t = f - 1;
                            3 <= t && 0 === e.bl_tree[2 * k[t] + 1];
                            t--
                          );
                          return ((e.opt_len += 3 * (t + 1) + 5 + 5 + 4), t);
                        })(e)),
                        (s = (e.opt_len + 3 + 7) >>> 3),
                        (a = (e.static_len + 3 + 7) >>> 3) <= s && (s = a))
                      : (s = a = r + 5),
                      r + 4 <= s && -1 !== t
                        ? Y(e, t, r, n)
                        : 4 === e.strategy || a === s
                          ? (P(e, 2 + (n ? 1 : 0), 3), K(e, S, I))
                          : (P(e, 4 + (n ? 1 : 0), 3),
                            (function (e, t, r, n) {
                              var i;
                              for (
                                P(e, t - 257, 5),
                                  P(e, r - 1, 5),
                                  P(e, n - 4, 4),
                                  i = 0;
                                i < n;
                                i++
                              )
                                P(e, e.bl_tree[2 * k[i] + 1], 3);
                              (q(e, e.dyn_ltree, t - 1),
                                q(e, e.dyn_dtree, r - 1));
                            })(
                              e,
                              e.l_desc.max_code + 1,
                              e.d_desc.max_code + 1,
                              d + 1,
                            ),
                            K(e, e.dyn_ltree, e.dyn_dtree)),
                      W(e),
                      n && Z(e));
                  }),
                  (r._tr_tally = function (e, t, r) {
                    return (
                      (e.pending_buf[e.d_buf + 2 * e.last_lit] =
                        (t >>> 8) & 255),
                      (e.pending_buf[e.d_buf + 2 * e.last_lit + 1] = 255 & t),
                      (e.pending_buf[e.l_buf + e.last_lit] = 255 & r),
                      e.last_lit++,
                      0 === t
                        ? e.dyn_ltree[2 * r]++
                        : (e.matches++,
                          t--,
                          e.dyn_ltree[2 * (O[r] + l + 1)]++,
                          e.dyn_dtree[2 * M(t)]++),
                      e.last_lit === e.lit_bufsize - 1
                    );
                  }),
                  (r._tr_align = function (e) {
                    (P(e, 2, 3),
                      N(e, w, S),
                      (function (e) {
                        16 === e.bi_valid
                          ? (R(e, e.bi_buf), (e.bi_buf = 0), (e.bi_valid = 0))
                          : 8 <= e.bi_valid &&
                            ((e.pending_buf[e.pending++] = 255 & e.bi_buf),
                            (e.bi_buf >>= 8),
                            (e.bi_valid -= 8));
                      })(e));
                  }));
              },
              { "../utils/common": 41 },
            ],
            53: [
              function (e, t, r) {
                "use strict";
                t.exports = function () {
                  ((this.input = null),
                    (this.next_in = 0),
                    (this.avail_in = 0),
                    (this.total_in = 0),
                    (this.output = null),
                    (this.next_out = 0),
                    (this.avail_out = 0),
                    (this.total_out = 0),
                    (this.msg = ""),
                    (this.state = null),
                    (this.data_type = 2),
                    (this.adler = 0));
                };
              },
              {},
            ],
            54: [
              function (e, t, r) {
                (function (e) {
                  !(function (e, t) {
                    "use strict";
                    if (!e.setImmediate) {
                      var r,
                        n,
                        i,
                        o,
                        s = 1,
                        a = {},
                        d = !1,
                        l = e.document,
                        c = Object.getPrototypeOf && Object.getPrototypeOf(e);
                      ((c = c && c.setTimeout ? c : e),
                        (r =
                          "[object process]" === {}.toString.call(e.process)
                            ? function (e) {
                                process.nextTick(function () {
                                  f(e);
                                });
                              }
                            : (function () {
                                  if (e.postMessage && !e.importScripts) {
                                    var t = !0,
                                      r = e.onmessage;
                                    return (
                                      (e.onmessage = function () {
                                        t = !1;
                                      }),
                                      e.postMessage("", "*"),
                                      (e.onmessage = r),
                                      t
                                    );
                                  }
                                })()
                              ? ((o = "setImmediate$" + Math.random() + "$"),
                                e.addEventListener
                                  ? e.addEventListener("message", h, !1)
                                  : e.attachEvent("onmessage", h),
                                function (t) {
                                  e.postMessage(o + t, "*");
                                })
                              : e.MessageChannel
                                ? (((i = new MessageChannel()).port1.onmessage =
                                    function (e) {
                                      f(e.data);
                                    }),
                                  function (e) {
                                    i.port2.postMessage(e);
                                  })
                                : l &&
                                    "onreadystatechange" in
                                      l.createElement("script")
                                  ? ((n = l.documentElement),
                                    function (e) {
                                      var t = l.createElement("script");
                                      ((t.onreadystatechange = function () {
                                        (f(e),
                                          (t.onreadystatechange = null),
                                          n.removeChild(t),
                                          (t = null));
                                      }),
                                        n.appendChild(t));
                                    })
                                  : function (e) {
                                      setTimeout(f, 0, e);
                                    }),
                        (c.setImmediate = function (e) {
                          "function" != typeof e && (e = new Function("" + e));
                          for (
                            var t = new Array(arguments.length - 1), n = 0;
                            n < t.length;
                            n++
                          )
                            t[n] = arguments[n + 1];
                          var i = { callback: e, args: t };
                          return ((a[s] = i), r(s), s++);
                        }),
                        (c.clearImmediate = u));
                    }
                    function u(e) {
                      delete a[e];
                    }
                    function f(e) {
                      if (d) setTimeout(f, 0, e);
                      else {
                        var r = a[e];
                        if (r) {
                          d = !0;
                          try {
                            !(function (e) {
                              var r = e.callback,
                                n = e.args;
                              switch (n.length) {
                                case 0:
                                  r();
                                  break;
                                case 1:
                                  r(n[0]);
                                  break;
                                case 2:
                                  r(n[0], n[1]);
                                  break;
                                case 3:
                                  r(n[0], n[1], n[2]);
                                  break;
                                default:
                                  r.apply(t, n);
                              }
                            })(r);
                          } finally {
                            (u(e), (d = !1));
                          }
                        }
                      }
                    }
                    function h(t) {
                      t.source === e &&
                        "string" == typeof t.data &&
                        0 === t.data.indexOf(o) &&
                        f(+t.data.slice(o.length));
                    }
                  })(
                    "undefined" == typeof self
                      ? void 0 === e
                        ? this
                        : e
                      : self,
                  );
                }).call(
                  this,
                  "undefined" != typeof global
                    ? global
                    : "undefined" != typeof self
                      ? self
                      : "undefined" != typeof window
                        ? window
                        : {},
                );
              },
              {},
            ],
          },
          {},
          [10],
        )(10);
      },
      150: function (e, t) {
        var r, n, i;
        ("undefined" != typeof globalThis
          ? globalThis
          : "undefined" != typeof self && self,
          (n = [e]),
          (r = function (e) {
            "use strict";
            if (
              !(
                globalThis.chrome &&
                globalThis.chrome.runtime &&
                globalThis.chrome.runtime.id
              )
            )
              throw new Error(
                "This script should only be loaded in a browser extension.",
              );
            if (
              globalThis.browser &&
              globalThis.browser.runtime &&
              globalThis.browser.runtime.id
            )
              e.exports = globalThis.browser;
            else {
              const t =
                  "The message port closed before a response was received.",
                r = (e) => {
                  const r = {
                    alarms: {
                      clear: { minArgs: 0, maxArgs: 1 },
                      clearAll: { minArgs: 0, maxArgs: 0 },
                      get: { minArgs: 0, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                    },
                    bookmarks: {
                      create: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getChildren: { minArgs: 1, maxArgs: 1 },
                      getRecent: { minArgs: 1, maxArgs: 1 },
                      getSubTree: { minArgs: 1, maxArgs: 1 },
                      getTree: { minArgs: 0, maxArgs: 0 },
                      move: { minArgs: 2, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeTree: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    browserAction: {
                      disable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      enable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      getBadgeBackgroundColor: { minArgs: 1, maxArgs: 1 },
                      getBadgeText: { minArgs: 1, maxArgs: 1 },
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      openPopup: { minArgs: 0, maxArgs: 0 },
                      setBadgeBackgroundColor: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setBadgeText: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    browsingData: {
                      remove: { minArgs: 2, maxArgs: 2 },
                      removeCache: { minArgs: 1, maxArgs: 1 },
                      removeCookies: { minArgs: 1, maxArgs: 1 },
                      removeDownloads: { minArgs: 1, maxArgs: 1 },
                      removeFormData: { minArgs: 1, maxArgs: 1 },
                      removeHistory: { minArgs: 1, maxArgs: 1 },
                      removeLocalStorage: { minArgs: 1, maxArgs: 1 },
                      removePasswords: { minArgs: 1, maxArgs: 1 },
                      removePluginData: { minArgs: 1, maxArgs: 1 },
                      settings: { minArgs: 0, maxArgs: 0 },
                    },
                    commands: { getAll: { minArgs: 0, maxArgs: 0 } },
                    contextMenus: {
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeAll: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    cookies: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 1, maxArgs: 1 },
                      getAllCookieStores: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    },
                    devtools: {
                      inspectedWindow: {
                        eval: { minArgs: 1, maxArgs: 2, singleCallbackArg: !1 },
                      },
                      panels: {
                        create: {
                          minArgs: 3,
                          maxArgs: 3,
                          singleCallbackArg: !0,
                        },
                        elements: {
                          createSidebarPane: { minArgs: 1, maxArgs: 1 },
                        },
                      },
                    },
                    downloads: {
                      cancel: { minArgs: 1, maxArgs: 1 },
                      download: { minArgs: 1, maxArgs: 1 },
                      erase: { minArgs: 1, maxArgs: 1 },
                      getFileIcon: { minArgs: 1, maxArgs: 2 },
                      open: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      pause: { minArgs: 1, maxArgs: 1 },
                      removeFile: { minArgs: 1, maxArgs: 1 },
                      resume: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    extension: {
                      isAllowedFileSchemeAccess: { minArgs: 0, maxArgs: 0 },
                      isAllowedIncognitoAccess: { minArgs: 0, maxArgs: 0 },
                    },
                    history: {
                      addUrl: { minArgs: 1, maxArgs: 1 },
                      deleteAll: { minArgs: 0, maxArgs: 0 },
                      deleteRange: { minArgs: 1, maxArgs: 1 },
                      deleteUrl: { minArgs: 1, maxArgs: 1 },
                      getVisits: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                    },
                    i18n: {
                      detectLanguage: { minArgs: 1, maxArgs: 1 },
                      getAcceptLanguages: { minArgs: 0, maxArgs: 0 },
                    },
                    identity: { launchWebAuthFlow: { minArgs: 1, maxArgs: 1 } },
                    idle: { queryState: { minArgs: 1, maxArgs: 1 } },
                    management: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getSelf: { minArgs: 0, maxArgs: 0 },
                      setEnabled: { minArgs: 2, maxArgs: 2 },
                      uninstallSelf: { minArgs: 0, maxArgs: 1 },
                    },
                    notifications: {
                      clear: { minArgs: 1, maxArgs: 1 },
                      create: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getPermissionLevel: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    pageAction: {
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      hide: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    permissions: {
                      contains: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      request: { minArgs: 1, maxArgs: 1 },
                    },
                    runtime: {
                      getBackgroundPage: { minArgs: 0, maxArgs: 0 },
                      getPlatformInfo: { minArgs: 0, maxArgs: 0 },
                      openOptionsPage: { minArgs: 0, maxArgs: 0 },
                      requestUpdateCheck: { minArgs: 0, maxArgs: 0 },
                      sendMessage: { minArgs: 1, maxArgs: 3 },
                      sendNativeMessage: { minArgs: 2, maxArgs: 2 },
                      setUninstallURL: { minArgs: 1, maxArgs: 1 },
                    },
                    sessions: {
                      getDevices: { minArgs: 0, maxArgs: 1 },
                      getRecentlyClosed: { minArgs: 0, maxArgs: 1 },
                      restore: { minArgs: 0, maxArgs: 1 },
                    },
                    storage: {
                      local: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                      managed: {
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                      },
                      sync: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                    },
                    tabs: {
                      captureVisibleTab: { minArgs: 0, maxArgs: 2 },
                      create: { minArgs: 1, maxArgs: 1 },
                      detectLanguage: { minArgs: 0, maxArgs: 1 },
                      discard: { minArgs: 0, maxArgs: 1 },
                      duplicate: { minArgs: 1, maxArgs: 1 },
                      executeScript: { minArgs: 1, maxArgs: 2 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 0 },
                      getZoom: { minArgs: 0, maxArgs: 1 },
                      getZoomSettings: { minArgs: 0, maxArgs: 1 },
                      goBack: { minArgs: 0, maxArgs: 1 },
                      goForward: { minArgs: 0, maxArgs: 1 },
                      highlight: { minArgs: 1, maxArgs: 1 },
                      insertCSS: { minArgs: 1, maxArgs: 2 },
                      move: { minArgs: 2, maxArgs: 2 },
                      query: { minArgs: 1, maxArgs: 1 },
                      reload: { minArgs: 0, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeCSS: { minArgs: 1, maxArgs: 2 },
                      sendMessage: { minArgs: 2, maxArgs: 3 },
                      setZoom: { minArgs: 1, maxArgs: 2 },
                      setZoomSettings: { minArgs: 1, maxArgs: 2 },
                      update: { minArgs: 1, maxArgs: 2 },
                    },
                    topSites: { get: { minArgs: 0, maxArgs: 0 } },
                    webNavigation: {
                      getAllFrames: { minArgs: 1, maxArgs: 1 },
                      getFrame: { minArgs: 1, maxArgs: 1 },
                    },
                    webRequest: {
                      handlerBehaviorChanged: { minArgs: 0, maxArgs: 0 },
                    },
                    windows: {
                      create: { minArgs: 0, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 1 },
                      getLastFocused: { minArgs: 0, maxArgs: 1 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                  };
                  if (0 === Object.keys(r).length)
                    throw new Error(
                      "api-metadata.json has not been included in browser-polyfill",
                    );
                  class n extends WeakMap {
                    constructor(e, t = void 0) {
                      (super(t), (this.createItem = e));
                    }
                    get(e) {
                      return (
                        this.has(e) || this.set(e, this.createItem(e)),
                        super.get(e)
                      );
                    }
                  }
                  const i = (e) =>
                      e && "object" == typeof e && "function" == typeof e.then,
                    o =
                      (t, r) =>
                      (...n) => {
                        e.runtime.lastError
                          ? t.reject(new Error(e.runtime.lastError.message))
                          : r.singleCallbackArg ||
                              (n.length <= 1 && !1 !== r.singleCallbackArg)
                            ? t.resolve(n[0])
                            : t.resolve(n);
                      },
                    s = (e) => (1 == e ? "argument" : "arguments"),
                    a = (e, t) =>
                      function (r, ...n) {
                        if (n.length < t.minArgs)
                          throw new Error(
                            `Expected at least ${t.minArgs} ${s(t.minArgs)} for ${e}(), got ${n.length}`,
                          );
                        if (n.length > t.maxArgs)
                          throw new Error(
                            `Expected at most ${t.maxArgs} ${s(t.maxArgs)} for ${e}(), got ${n.length}`,
                          );
                        return new Promise((i, s) => {
                          if (t.fallbackToNoCallback)
                            try {
                              r[e](...n, o({ resolve: i, reject: s }, t));
                            } catch (o) {
                              (console.warn(
                                `${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,
                                o,
                              ),
                                r[e](...n),
                                (t.fallbackToNoCallback = !1),
                                (t.noCallback = !0),
                                i());
                            }
                          else
                            t.noCallback
                              ? (r[e](...n), i())
                              : r[e](...n, o({ resolve: i, reject: s }, t));
                        });
                      },
                    d = (e, t, r) =>
                      new Proxy(t, { apply: (t, n, i) => r.call(n, e, ...i) });
                  let l = Function.call.bind(Object.prototype.hasOwnProperty);
                  const c = (e, t = {}, r = {}) => {
                      let n = Object.create(null),
                        i = {
                          has: (t, r) => r in e || r in n,
                          get(i, o, s) {
                            if (o in n) return n[o];
                            if (!(o in e)) return;
                            let u = e[o];
                            if ("function" == typeof u)
                              if ("function" == typeof t[o])
                                u = d(e, e[o], t[o]);
                              else if (l(r, o)) {
                                let t = a(o, r[o]);
                                u = d(e, e[o], t);
                              } else u = u.bind(e);
                            else if (
                              "object" == typeof u &&
                              null !== u &&
                              (l(t, o) || l(r, o))
                            )
                              u = c(u, t[o], r[o]);
                            else {
                              if (!l(r, "*"))
                                return (
                                  Object.defineProperty(n, o, {
                                    configurable: !0,
                                    enumerable: !0,
                                    get: () => e[o],
                                    set(t) {
                                      e[o] = t;
                                    },
                                  }),
                                  u
                                );
                              u = c(u, t[o], r["*"]);
                            }
                            return ((n[o] = u), u);
                          },
                          set: (t, r, i, o) => (
                            r in n ? (n[r] = i) : (e[r] = i),
                            !0
                          ),
                          defineProperty: (e, t, r) =>
                            Reflect.defineProperty(n, t, r),
                          deleteProperty: (e, t) =>
                            Reflect.deleteProperty(n, t),
                        },
                        o = Object.create(e);
                      return new Proxy(o, i);
                    },
                    u = (e) => ({
                      addListener(t, r, ...n) {
                        t.addListener(e.get(r), ...n);
                      },
                      hasListener: (t, r) => t.hasListener(e.get(r)),
                      removeListener(t, r) {
                        t.removeListener(e.get(r));
                      },
                    }),
                    f = new n((e) =>
                      "function" != typeof e
                        ? e
                        : function (t) {
                            const r = c(
                              t,
                              {},
                              { getContent: { minArgs: 0, maxArgs: 0 } },
                            );
                            e(r);
                          },
                    ),
                    h = new n((e) =>
                      "function" != typeof e
                        ? e
                        : function (t, r, n) {
                            let o,
                              s,
                              a = !1,
                              d = new Promise((e) => {
                                o = function (t) {
                                  ((a = !0), e(t));
                                };
                              });
                            try {
                              s = e(t, r, o);
                            } catch (e) {
                              s = Promise.reject(e);
                            }
                            const l = !0 !== s && i(s);
                            if (!0 !== s && !l && !a) return !1;
                            const c = (e) => {
                              e.then(
                                (e) => {
                                  n(e);
                                },
                                (e) => {
                                  let t;
                                  ((t =
                                    e &&
                                    (e instanceof Error ||
                                      "string" == typeof e.message)
                                      ? e.message
                                      : "An unexpected error occurred"),
                                    n({
                                      __mozWebExtensionPolyfillReject__: !0,
                                      message: t,
                                    }));
                                },
                              ).catch((e) => {
                                console.error(
                                  "Failed to send onMessage rejected reply",
                                  e,
                                );
                              });
                            };
                            return (c(l ? s : d), !0);
                          },
                    ),
                    m = ({ reject: r, resolve: n }, i) => {
                      e.runtime.lastError
                        ? e.runtime.lastError.message === t
                          ? n()
                          : r(new Error(e.runtime.lastError.message))
                        : i && i.__mozWebExtensionPolyfillReject__
                          ? r(new Error(i.message))
                          : n(i);
                    },
                    p = (e, t, r, ...n) => {
                      if (n.length < t.minArgs)
                        throw new Error(
                          `Expected at least ${t.minArgs} ${s(t.minArgs)} for ${e}(), got ${n.length}`,
                        );
                      if (n.length > t.maxArgs)
                        throw new Error(
                          `Expected at most ${t.maxArgs} ${s(t.maxArgs)} for ${e}(), got ${n.length}`,
                        );
                      return new Promise((e, t) => {
                        const i = m.bind(null, { resolve: e, reject: t });
                        (n.push(i), r.sendMessage(...n));
                      });
                    },
                    g = {
                      devtools: { network: { onRequestFinished: u(f) } },
                      runtime: {
                        onMessage: u(h),
                        onMessageExternal: u(h),
                        sendMessage: p.bind(null, "sendMessage", {
                          minArgs: 1,
                          maxArgs: 3,
                        }),
                      },
                      tabs: {
                        sendMessage: p.bind(null, "sendMessage", {
                          minArgs: 2,
                          maxArgs: 3,
                        }),
                      },
                    },
                    w = {
                      clear: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    };
                  return (
                    (r.privacy = {
                      network: { "*": w },
                      services: { "*": w },
                      websites: { "*": w },
                    }),
                    c(e, g, r)
                  );
                };
              e.exports = r(chrome);
            }
          }),
          void 0 === (i = "function" == typeof r ? r.apply(t, n) : r) ||
            (e.exports = i));
      },
    },
    t = {};
  function r(n) {
    var i = t[n];
    if (void 0 !== i) return i.exports;
    var o = (t[n] = { exports: {} });
    return (e[n].call(o.exports, o, o.exports, r), o.exports);
  }
  ((r.n = (e) => {
    var t = e && e.__esModule ? () => e.default : () => e;
    return (r.d(t, { a: t }), t);
  }),
    (r.d = (e, t) => {
      for (var n in t)
        r.o(t, n) &&
          !r.o(e, n) &&
          Object.defineProperty(e, n, { enumerable: !0, get: t[n] });
    }),
    (r.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t)));
  var n = {};
  (() => {
    "use strict";
    r.d(n, { d: () => J });
    function e(e, t, r, n) {
      return new (r || (r = Promise))(function (i, o) {
        function s(e) {
          try {
            d(n.next(e));
          } catch (e) {
            o(e);
          }
        }
        function a(e) {
          try {
            d(n.throw(e));
          } catch (e) {
            o(e);
          }
        }
        function d(e) {
          var t;
          e.done
            ? i(e.value)
            : ((t = e.value),
              t instanceof r
                ? t
                : new r(function (e) {
                    e(t);
                  })).then(s, a);
        }
        d((n = n.apply(e, t || [])).next());
      });
    }
    Object.create;
    Object.create;
    var t = r(150),
      i = r.n(t);
    var o,
      s,
      a,
      d,
      l,
      c,
      u,
      f,
      h,
      m,
      p,
      g,
      w = (function () {
        var e = "undefined" != typeof self ? self : this,
          t = {
            navigator: void 0 !== e.navigator ? e.navigator : {},
            infoMap: {
              engine: ["WebKit", "Trident", "Gecko", "Presto"],
              browser: [
                "Safari",
                "Chrome",
                "Edge",
                "IE",
                "Firefox",
                "Firefox Focus",
                "Chromium",
                "Opera",
                "Vivaldi",
                "Yandex",
                "Arora",
                "Lunascape",
                "QupZilla",
                "Coc Coc",
                "Kindle",
                "Iceweasel",
                "Konqueror",
                "Iceape",
                "SeaMonkey",
                "Epiphany",
                "360",
                "360SE",
                "360EE",
                "UC",
                "QQBrowser",
                "QQ",
                "Baidu",
                "Maxthon",
                "Sogou",
                "LBBROWSER",
                "2345Explorer",
                "TheWorld",
                "XiaoMi",
                "Quark",
                "Qiyu",
                "Wechat",
                ,
                "WechatWork",
                "Taobao",
                "Alipay",
                "Weibo",
                "Douban",
                "Suning",
                "iQiYi",
              ],
              os: [
                "Windows",
                "Linux",
                "Mac OS",
                "Android",
                "Ubuntu",
                "FreeBSD",
                "Debian",
                "iOS",
                "Windows Phone",
                "BlackBerry",
                "MeeGo",
                "Symbian",
                "Chrome OS",
                "WebOS",
              ],
              device: ["Mobile", "Tablet", "iPad"],
            },
          },
          r = {
            createUUID: function () {
              for (var e = [], t = "0123456789abcdef", r = 0; r < 36; r++)
                e[r] = t.substr(Math.floor(16 * Math.random()), 1);
              return (
                (e[14] = "4"),
                (e[19] = t.substr((3 & e[19]) | 8, 1)),
                (e[8] = e[13] = e[18] = e[23] = "-"),
                e.join("")
              );
            },
            getDate: function () {
              var e = new Date(),
                t = e.getFullYear(),
                r = e.getMonth() + 1,
                n = e.getDate(),
                i = e.getHours(),
                o = e.getMinutes(),
                s = e.getSeconds();
              return ""
                .concat(t.toString(), "/")
                .concat(r.toString(), "/")
                .concat(n.toString(), " ")
                .concat(i.toString(), ":")
                .concat(o.toString(), ":")
                .concat(s.toString());
            },
            getTimezoneOffset: function () {
              return new Date().getTimezoneOffset();
            },
            getTimezone: function () {
              return Intl.DateTimeFormat().resolvedOptions().timeZone;
            },
            getMatchMap: function (e) {
              return {
                Trident: e.indexOf("Trident") > -1 || e.indexOf("NET CLR") > -1,
                Presto: e.indexOf("Presto") > -1,
                WebKit: e.indexOf("AppleWebKit") > -1,
                Gecko: e.indexOf("Gecko/") > -1,
                Safari: e.indexOf("Safari") > -1,
                Chrome: e.indexOf("Chrome") > -1 || e.indexOf("CriOS") > -1,
                IE: e.indexOf("MSIE") > -1 || e.indexOf("Trident") > -1,
                Edge: e.indexOf("Edge") > -1,
                Firefox: e.indexOf("Firefox") > -1 || e.indexOf("FxiOS") > -1,
                "Firefox Focus": e.indexOf("Focus") > -1,
                Chromium: e.indexOf("Chromium") > -1,
                Opera: e.indexOf("Opera") > -1 || e.indexOf("OPR") > -1,
                Vivaldi: e.indexOf("Vivaldi") > -1,
                Yandex: e.indexOf("YaBrowser") > -1,
                Arora: e.indexOf("Arora") > -1,
                Lunascape: e.indexOf("Lunascape") > -1,
                QupZilla: e.indexOf("QupZilla") > -1,
                "Coc Coc": e.indexOf("coc_coc_browser") > -1,
                Kindle: e.indexOf("Kindle") > -1 || e.indexOf("Silk/") > -1,
                Iceweasel: e.indexOf("Iceweasel") > -1,
                Konqueror: e.indexOf("Konqueror") > -1,
                Iceape: e.indexOf("Iceape") > -1,
                SeaMonkey: e.indexOf("SeaMonkey") > -1,
                Epiphany: e.indexOf("Epiphany") > -1,
                360:
                  e.indexOf("QihooBrowser") > -1 || e.indexOf("QHBrowser") > -1,
                "360EE": e.indexOf("360EE") > -1,
                "360SE": e.indexOf("360SE") > -1,
                UC: e.indexOf("UC") > -1 || e.indexOf(" UBrowser") > -1,
                QQBrowser: e.indexOf("QQBrowser") > -1,
                QQ: e.indexOf("QQ/") > -1,
                Baidu: e.indexOf("Baidu") > -1 || e.indexOf("BIDUBrowser") > -1,
                Maxthon: e.indexOf("Maxthon") > -1,
                Sogou: e.indexOf("MetaSr") > -1 || e.indexOf("Sogou") > -1,
                LBBROWSER:
                  e.indexOf("LBBROWSER") > -1 || e.indexOf("LieBaoFast") > -1,
                "2345Explorer": e.indexOf("2345Explorer") > -1,
                TheWorld: e.indexOf("TheWorld") > -1,
                XiaoMi: e.indexOf("MiuiBrowser") > -1,
                Quark: e.indexOf("Quark") > -1,
                Qiyu: e.indexOf("Qiyu") > -1,
                Wechat: e.indexOf("MicroMessenger") > -1,
                WechatWork: e.indexOf("wxwork/") > -1,
                Taobao: e.indexOf("AliApp(TB") > -1,
                Alipay: e.indexOf("AliApp(AP") > -1,
                Weibo: e.indexOf("Weibo") > -1,
                Douban: e.indexOf("com.douban.frodo") > -1,
                Suning: e.indexOf("SNEBUY-APP") > -1,
                iQiYi: e.indexOf("IqiyiApp") > -1,
                DingTalk: e.indexOf("DingTalk") > -1,
                Vivo: e.indexOf("VivoBrowser") > -1,
                Huawei:
                  e.indexOf("HuaweiBrowser") > -1 ||
                  e.indexOf("HUAWEI/") > -1 ||
                  e.indexOf("HONOR") > -1 ||
                  e.indexOf("HBPC/") > -1,
                Windows: e.indexOf("Windows") > -1,
                Linux: e.indexOf("Linux") > -1 || e.indexOf("X11") > -1,
                "Mac OS": e.indexOf("Macintosh") > -1,
                Android: e.indexOf("Android") > -1 || e.indexOf("Adr") > -1,
                Ubuntu: e.indexOf("Ubuntu") > -1,
                FreeBSD: e.indexOf("FreeBSD") > -1,
                Debian: e.indexOf("Debian") > -1,
                "Windows Phone":
                  e.indexOf("IEMobile") > -1 || e.indexOf("Windows Phone") > -1,
                BlackBerry:
                  e.indexOf("BlackBerry") > -1 || e.indexOf("RIM") > -1,
                MeeGo: e.indexOf("MeeGo") > -1,
                Symbian: e.indexOf("Symbian") > -1,
                iOS: e.indexOf("like Mac OS X") > -1,
                "Chrome OS": e.indexOf("CrOS") > -1,
                WebOS: e.indexOf("hpwOS") > -1,
                Mobile:
                  e.indexOf("Mobi") > -1 ||
                  e.indexOf("iPh") > -1 ||
                  e.indexOf("480") > -1,
                Tablet: e.indexOf("Tablet") > -1 || e.indexOf("Nexus 7") > -1,
                iPad: e.indexOf("iPad") > -1,
              };
            },
            matchInfoMap: function (e) {
              var n = t.navigator.userAgent || {},
                i = r.getMatchMap(n);
              for (var o in t.infoMap)
                for (var s = 0; s < t.infoMap[o].length; s++) {
                  var a = t.infoMap[o][s];
                  i[a] && (e[o] = a);
                }
            },
            getOS: function () {
              return (r.matchInfoMap(this), this.os);
            },
            getOSVersion: function () {
              var e = this,
                r = t.navigator.userAgent || {};
              e.osVersion = "";
              var n = {
                Windows: function () {
                  var e = r.replace(/^.*Windows NT ([\d.]+);.*$/, "$1");
                  return (
                    {
                      10: "10 || 11",
                      6.3: "8.1",
                      6.2: "8",
                      6.1: "7",
                      "6.0": "Vista",
                      5.2: "XP 64-Bit",
                      5.1: "XP",
                      "5.0": "2000",
                      "4.0": "NT 4.0",
                      "3.5.1": "NT 3.5.1",
                      3.5: "NT 3.5",
                      3.1: "NT 3.1",
                    }[e] || e
                  );
                },
                Android: function () {
                  return r.replace(/^.*Android ([\d.]+);.*$/, "$1");
                },
                iOS: function () {
                  return r
                    .replace(/^.*OS ([\d_]+) like.*$/, "$1")
                    .replace(/_/g, ".");
                },
                Debian: function () {
                  return r.replace(/^.*Debian\/([\d.]+).*$/, "$1");
                },
                "Windows Phone": function () {
                  return r.replace(/^.*Windows Phone( OS)? ([\d.]+);.*$/, "$2");
                },
                "Mac OS": function () {
                  return r
                    .replace(/^.*Mac OS X ([\d_]+).*$/, "$1")
                    .replace(/_/g, ".");
                },
                WebOS: function () {
                  return r.replace(/^.*hpwOS\/([\d.]+);.*$/, "$1");
                },
              };
              return (
                n[e.os] &&
                  ((e.osVersion = n[e.os]()),
                  e.osVersion == r && (e.osVersion = "")),
                e.osVersion
              );
            },
            getDeviceType: function () {
              var e = this;
              return ((e.device = "PC"), r.matchInfoMap(e), e.device);
            },
            getNetwork: function () {
              return "";
            },
            getLanguage: function () {
              var e;
              return (
                (this.language =
                  ((e = (
                    t.navigator.browserLanguage || t.navigator.language
                  ).split("-"))[1] && (e[1] = e[1].toUpperCase()),
                  e.join("_"))),
                this.language
              );
            },
            getBrowserInfo: function () {
              var e = this;
              r.matchInfoMap(e);
              var n = t.navigator.userAgent || {},
                i = r.getMatchMap(n);
              if (
                (i.Baidu && i.Opera && (i.Baidu = !1),
                i.Mobile && (i.Mobile = !(n.indexOf("iPad") > -1)),
                i.IE || i.Edge)
              )
                switch (window.screenTop - window.screenY) {
                  case 71:
                  case 74:
                  case 99:
                  case 75:
                  case 74:
                  case 105:
                  default:
                    break;
                  case 102:
                    i["360EE"] = !0;
                    break;
                  case 104:
                    i["360SE"] = !0;
                }
              var o = {
                Safari: function () {
                  return n.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Chrome: function () {
                  return n
                    .replace(/^.*Chrome\/([\d.]+).*$/, "$1")
                    .replace(/^.*CriOS\/([\d.]+).*$/, "$1");
                },
                IE: function () {
                  return n
                    .replace(/^.*MSIE ([\d.]+).*$/, "$1")
                    .replace(/^.*rv:([\d.]+).*$/, "$1");
                },
                Edge: function () {
                  return n.replace(/^.*Edge\/([\d.]+).*$/, "$1");
                },
                Firefox: function () {
                  return n
                    .replace(/^.*Firefox\/([\d.]+).*$/, "$1")
                    .replace(/^.*FxiOS\/([\d.]+).*$/, "$1");
                },
                "Firefox Focus": function () {
                  return n.replace(/^.*Focus\/([\d.]+).*$/, "$1");
                },
                Chromium: function () {
                  return n.replace(/^.*Chromium\/([\d.]+).*$/, "$1");
                },
                Opera: function () {
                  return n
                    .replace(/^.*Opera\/([\d.]+).*$/, "$1")
                    .replace(/^.*OPR\/([\d.]+).*$/, "$1");
                },
                Vivaldi: function () {
                  return n.replace(/^.*Vivaldi\/([\d.]+).*$/, "$1");
                },
                Yandex: function () {
                  return n.replace(/^.*YaBrowser\/([\d.]+).*$/, "$1");
                },
                Arora: function () {
                  return n.replace(/^.*Arora\/([\d.]+).*$/, "$1");
                },
                Lunascape: function () {
                  return n.replace(/^.*Lunascape[\/\s]([\d.]+).*$/, "$1");
                },
                QupZilla: function () {
                  return n.replace(/^.*QupZilla[\/\s]([\d.]+).*$/, "$1");
                },
                "Coc Coc": function () {
                  return n.replace(/^.*coc_coc_browser\/([\d.]+).*$/, "$1");
                },
                Kindle: function () {
                  return n.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Iceweasel: function () {
                  return n.replace(/^.*Iceweasel\/([\d.]+).*$/, "$1");
                },
                Konqueror: function () {
                  return n.replace(/^.*Konqueror\/([\d.]+).*$/, "$1");
                },
                Iceape: function () {
                  return n.replace(/^.*Iceape\/([\d.]+).*$/, "$1");
                },
                SeaMonkey: function () {
                  return n.replace(/^.*SeaMonkey\/([\d.]+).*$/, "$1");
                },
                Epiphany: function () {
                  return n.replace(/^.*Epiphany\/([\d.]+).*$/, "$1");
                },
                Maxthon: function () {
                  return n.replace(/^.*Maxthon\/([\d.]+).*$/, "$1");
                },
              };
              return (
                (e.browserVersion = ""),
                o[e.browser] &&
                  ((e.browserVersion = o[e.browser]()),
                  e.browserVersion == n && (e.browserVersion = "")),
                "Chrome" == e.browser &&
                  n.match(/\S+Browser/) &&
                  ((e.browser = n.match(/\S+Browser/)[0]),
                  (e.version = n.replace(/^.*Browser\/([\d.]+).*$/, "$1"))),
                "Edge" == e.browser &&
                  (e.version > "75"
                    ? (e.engine = "Blink")
                    : (e.engine = "EdgeHTML")),
                (("Chrome" == e.browser && parseInt(e.browserVersion) > 27) ||
                  (i.Chrome &&
                    "WebKit" == e.engine &&
                    parseInt(o.Chrome()) > 27) ||
                  ("Opera" == e.browser && parseInt(e.version) > 12) ||
                  "Yandex" == e.browser) &&
                  (e.engine = "Blink"),
                e.browser +
                  "(version: " +
                  e.browserVersion +
                  "&nbsp;&nbsp;kernel: " +
                  e.engine +
                  ")"
              );
            },
            getGeoPostion: function () {
              return new Promise(function (e, t) {
                navigator && navigator.geolocation
                  ? navigator.geolocation.getCurrentPosition(
                      function (t) {
                        e(t);
                      },
                      function () {
                        e({ coords: { longitude: "fail", latitude: "fail" } });
                      },
                      { enableHighAccuracy: !1, timeout: 1e4 },
                    )
                  : t("fail");
              });
            },
            getPlatform: function () {
              return (
                (t.navigator.userAgentData &&
                  t.navigator.userAgentData.platform) ||
                t.navigator.platform
              );
            },
          },
          n = {
            DeviceInfoObj: function (e) {
              var n = {
                  deviceType: r.getDeviceType(),
                  os: r.getOS(),
                  osVersion: r.getOSVersion(),
                  platform: r.getPlatform(),
                  language: r.getLanguage(),
                  network: r.getNetwork(),
                  browserInfo: r.getBrowserInfo(),
                  userAgent: t.navigator.userAgent,
                  geoPosition: !0,
                  date: r.getDate(),
                  timezoneOffset: r.getTimezoneOffset(),
                  timezone: r.getTimezone(),
                  uuid: r.createUUID(),
                },
                i = {};
              if (e && e.info && 0 !== e.info.length) {
                var o = {},
                  s = function (t) {
                    e.info.forEach(function (e) {
                      e.toLowerCase() === t.toLowerCase() &&
                        (o[(e = t)] = n[e]);
                    });
                  };
                for (var a in n) s(a);
                i = o;
              } else i = n;
              return i;
            },
          };
        return {
          Info: function (e) {
            return n.DeviceInfoObj(e);
          },
        };
      })();
    function y() {
      return w.Info({
        info: [
          "deviceType",
          "OS",
          "OSVersion",
          "platform",
          "language",
          "netWork",
          "browserInfo",
          "screenHeight",
          "screenWidth",
          "userAgent",
          "appCodeName",
          "appName",
          "appVersion",
          "geoPosition",
          "date",
          "UUID",
          "timezoneOffset",
          "timezone",
        ],
      });
    }
    (!(function (e) {
      ((e[(e.pre = 0)] = "pre"),
        (e[(e.after = 1)] = "after"),
        (e[(e.getExtDrmKey = 2)] = "getExtDrmKey"));
    })(o || (o = {})),
      (function (e) {
        ((e[(e.single = 0)] = "single"),
          (e[(e.bulk = 1)] = "bulk"),
          (e[(e.bloburl = 2)] = "bloburl"),
          (e[(e.changeUrl = 3)] = "changeUrl"),
          (e[(e.login = 4)] = "login"),
          (e[(e.googleLogin = 5)] = "googleLogin"),
          (e[(e.register = 6)] = "register"),
          (e[(e.sendEmailCode = 7)] = "sendEmailCode"),
          (e[(e.getDrmSecretKey = 8)] = "getDrmSecretKey"),
          (e[(e.getConfig = 9)] = "getConfig"),
          (e[(e.getMemberInfo = 10)] = "getMemberInfo"),
          (e[(e.updateNoPerDayDownloadCount = 11)] =
            "updateNoPerDayDownloadCount"));
      })(s || (s = {})),
      (function (e) {
        ((e[(e.goSubscribe = 0)] = "goSubscribe"),
          (e[(e.pureNotice = 1)] = "pureNotice"),
          (e[(e.drmLicense = 2)] = "drmLicense"),
          (e[(e.retryMessage = 3)] = "retryMessage"),
          (e[(e.serverError = 4)] = "serverError"));
      })(a || (a = {})),
      (function (e) {
        ((e[(e.Edge = 0)] = "Edge"),
          (e[(e.Chrome = 1)] = "Chrome"),
          (e[(e.Firefox = 2)] = "Firefox"),
          (e[(e.Opera = 3)] = "Opera"),
          (e[(e.Safari = 4)] = "Safari"),
          (e[(e.Unknown = 5)] = "Unknown"));
      })(d || (d = {})),
      (function (e) {
        ((e.default = "log"), (e.warn = "warn"), (e.error = "error"));
      })(l || (l = {})),
      (function (e) {
        ((e.install = "install"),
          (e.uninstall = "uninstall"),
          (e.downloadSignalUnkown = "downloadSignalUnkown"),
          (e.downloadSignalImg = "downloadSignalImg"),
          (e.downloadSignalVideo = "downloadSignalVideo"),
          (e.downloadBulk = "downloadBulk"),
          (e.changeUrl = "changeUrl"),
          (e.register = "register"),
          (e.login = "login"),
          (e.googleLogin = "googleLogin"),
          (e.sendEmailCode = "sendEmailCode"),
          (e.uploadFiles = "uploadFiles"),
          (e.concatVideoAndAudio = "concatVideoAndAudio"));
      })(c || (c = {})),
      (function (e) {
        ((e.downloadSuccess = "downloadSuccess"),
          (e.downloadError = "downloadError"),
          (e.downloadCancle = "downloadCancle"),
          (e.downloadWating = "downloadWating"),
          (e.downloadPrepare = "downloadPrepare"),
          (e.downloadStuck = "downloadStuck"));
      })(u || (u = {})),
      (function (e) {
        ((e.addOrUpdateDownloadingInfo = "addOrUpdateDownloadingInfo"),
          (e.updateDownloadStatus = "updateDownloadStatus"));
      })(f || (f = {})),
      (function (e) {
        e[(e.refresh = 0)] = "refresh";
      })(h || (h = {})),
      (function (e) {
        ((e.downloading = "downloading"),
          (e.downloaded = "downloaded"),
          (e.download = "download"),
          (e.all = "all"),
          (e.quota = "quota"));
      })(m || (m = {})),
      (function (e) {
        ((e.processVideo = "processVideo"),
          (e.processVideoInWeb = "processVideoInWeb"),
          (e.processVideoByUrl = "processVideoByUrl"));
      })(p || (p = {})),
      (function (e) {
        ((e.serverError = "serverError"), (e.tip = "tip"));
      })(g || (g = {})));
    var v;
    !(function (t) {
      let r;
      const n = () => {
        var e;
        null === (e = document.getElementById("addOnInfoWrapperid")) ||
          void 0 === e ||
          e.remove();
        const t = document.createElement("div");
        return (
          (t.id = "addOnInfoWrapperid"),
          (t.innerHTML =
            '\n    <div class="modal" id="modal">\n        <div class="modal-header">\n            <div id="addon-info-title"></div>\n        </div>\n        <div class="modal-content">\n        </div>\n        <div class="modal-footer">            \n        </div>\n    </div>\n    '),
          document.body.appendChild(t),
          t
        );
      };
      function o(e, t, r) {
        const n = document.createElement("button");
        return (
          n.classList.add("btn", e),
          (n.textContent = t),
          n.addEventListener("click", function () {
            (null != r && r(), d());
          }),
          n
        );
      }
      function s() {
        const e = document.getElementById("modal");
        ((e.style.display = "block"),
          setTimeout(() => {
            e.classList.add("show");
          }, 10));
      }
      function d() {
        const e = document.getElementById("modal");
        (e.classList.remove("show"),
          setTimeout(() => {
            ((e.style.display = "none"), clearInterval(r));
          }, 300));
      }
      ((t.displayMessage = function (e, r = 10) {
        W(location.href) &&
          (e.type == a.goSubscribe
            ? (console.log(e),
              console.log(e.mainAction),
              e.mainAction && "blank" == e.mainAction
                ? (console.log(111),
                  t.openModalWithButton(
                    e.title,
                    e.text,
                    e.mainText,
                    () => {
                      window.open(e.mainUrl, "_blank");
                    },
                    e.subText,
                    null,
                  ))
                : t.openModalWithButton(
                    e.title,
                    e.text,
                    e.mainText,
                    () => {
                      window.location.href = e.mainUrl;
                    },
                    e.subText,
                    null,
                  ))
            : e.type == a.pureNotice &&
              t.openModalWithTimer(e.title, e.text, r));
      }),
        (t.openModalWithTimer = function (t, i, o = 10) {
          if (W(location.href)) {
            n();
            const a = document.getElementById("addon-info-title"),
              l = document.querySelector(".modal-content"),
              c = document.querySelector(".modal-footer");
            (clearInterval(r),
              (l.innerHTML = i),
              (c.innerHTML = ""),
              (a.innerHTML = t));
            const u = document.createElement("div");
            ((u.id = "countdown"),
              (u.textContent = `close in ${o}s`),
              c.appendChild(u));
            let f = o;
            r = setInterval(() => {
              f <= 0
                ? (clearInterval(r), d())
                : ((u.textContent = `close in ${f}s`), f--);
            }, 1e3);
            const h = document.querySelector(".noticButtonP .noticA");
            (h &&
              (h.onclick = () =>
                e(this, void 0, void 0, function* () {
                  let e = yield O("discordUrl");
                  (window.open(e + "", "_blank"), yield E("isJumpDiscord", !0));
                })),
              s());
          }
        }),
        (t.openModalWithButton = function (e, t, a, d, l, c) {
          if (W(location.href)) {
            n();
            const u = document.getElementById("addon-info-title"),
              f = document.querySelector(".modal-content"),
              h = document.querySelector(".modal-footer");
            if (
              (clearInterval(r),
              (f.innerHTML = t),
              (h.innerHTML = ""),
              (u.innerHTML = e),
              null != a)
            ) {
              const e = o("btn-wishlist", a, d);
              h.appendChild(e);
            }
            if (null != l) {
              const e = o("btn-no-thanks", l, c);
              h.appendChild(e);
            }
            const m = document.querySelector(".notice_openSiderpanle");
            (m &&
              (m.onclick = () => {
                let e = i().runtime.connect({ name: "openSidepanels" });
                (e.postMessage({}), e.disconnect());
              }),
              s());
          }
        }));
    })(v || (v = {}));
    const b = "udemyDownloadingInfo",
      _ = "udemyDownloadInfo",
      x = "udemyDownloadedInfo",
      A = 1e3;
    function k(e) {
      return new Promise((t) => setTimeout(t, e));
    }
    function S() {
      const e = navigator.userAgent;
      return e.includes("Edg")
        ? d.Edge
        : e.includes("OPR") || e.includes("Opera")
          ? d.Opera
          : e.includes("Chrome") && !e.includes("Chromium")
            ? d.Chrome
            : !e.includes("Safari") ||
                e.includes("Chrome") ||
                e.includes("Chromium")
              ? e.includes("Firefox")
                ? d.Firefox
                : d.Unknown
              : d.Safari;
    }
    function I() {
      return e(this, void 0, void 0, function* () {
        let e = yield O("UdemyDownloaderUserInfo");
        if ((console.log(e), null == e || null == e.userId || "" == e.userId)) {
          let t;
          return (
            (t =
              chrome && chrome.runtime && !chrome.runtime.id
                ? chrome.runtime.id
                : "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
                    /[xy]/g,
                    function (e) {
                      var t =
                        crypto.getRandomValues(new Uint8Array(1))[0] % 16 | 0;
                      return ("x" === e ? t : (3 & t) | 8).toString(16);
                    },
                  )),
            (e = {}),
            (e.userId = t),
            (e.memberName = "Guest"),
            (e.maxDownloadCount = parseInt("2")),
            (e.canBusiness = "0"),
            (e.allDownloadedCount = 0),
            (e.downloadedCount = 0),
            yield i()
              .storage.local.set({ UdemyDownloaderUserInfo: e })
              .then(() => {
                console.log("userInfo is set");
              }),
            e
          );
        }
        return e;
      });
    }
    function C() {
      return e(this, void 0, void 0, function* () {
        let e = yield O("UdemyDownloaderUserInfo");
        return null != e && null != e.userId && "" != e.userId ? e : null;
      });
    }
    function O(e) {
      return i()
        .storage.local.get(e)
        .then((t) => t[e]);
    }
    function E(e, t) {
      return i().storage.local.set({ [e]: t });
    }
    function D() {
      return e(this, void 0, void 0, function* () {
        let e = yield C(),
          t = yield O("downloadCount");
        if (((t = null == t ? 0 : t), t < e.maxDownloadCount))
          (yield E("downloadCount", t + 1),
            yield $.downloadRecord(e.userId, "1"));
        else {
          let t = e.noPerDayMemberList;
          if (null != t && t.length > 0) {
            for (let e = 0; e < t.length; e++) {
              let r = yield O(t[e].id + ""),
                n = null == r ? 0 : r;
              if (n < t[e].downloadCount) {
                let r = n + 1;
                yield E(t[e].id + "", r);
                let i = JSON.stringify({ id: t[e].id, downloadCount: r });
                yield $.updateNoPerDayDownloadCount(i);
                break;
              }
            }
            yield $.downloadRecord(e.userId, "0");
          } else yield $.downloadRecord(e.userId, "1");
        }
        let r = i().runtime.connect({ name: "sidepanelsNotification" }),
          n = { downloadId: "", action: h.refresh, refreshType: m.quota };
        r && (r.postMessage(n), r.disconnect());
      });
    }
    function T() {
      return e(this, void 0, void 0, function* () {
        let e = yield O("downloadSegmentsCount");
        return (e || (e = parseInt("15")), e);
      });
    }
    function U(t) {
      return e(this, void 0, void 0, function* () {
        let e = yield O(_);
        e || (e = []);
        const r = e.findIndex((e) => e.downloadId === t.downloadId),
          n = e.findIndex((e) => e.lectureId === t.lectureId);
        (-1 !== r
          ? (e[r] = Object.assign(Object.assign({}, e[r]), t))
          : -1 !== n
            ? (e[n] = Object.assign(Object.assign({}, e[n]), t))
            : e.push(t),
          yield E(_, e));
      });
    }
    const z = new (class {
      constructor() {
        this.locks = {};
      }
      acquire(t, r) {
        return e(this, void 0, void 0, function* () {
          const e = (this.locks[t] || Promise.resolve())
            .then(r)
            .catch(() => {});
          return ((this.locks[t] = e), e);
        });
      }
    })();
    function B(t) {
      return e(this, void 0, void 0, function* () {
        return (
          yield z.acquire(b, () =>
            e(this, void 0, void 0, function* () {
              let e = (yield O(b)) || [];
              const r = e.findIndex((e) => e.downloadId === t.downloadId);
              if (-1 !== r) e[r] = Object.assign(Object.assign({}, e[r]), t);
              else {
                const r = ((yield O(x)) || []).findIndex(
                  (e) => e.downloadId === t.downloadId,
                );
                (yield N(t.downloadId), -1 === r && e.unshift(t));
              }
              yield E(b, e);
            }),
          ),
          t
        );
      });
    }
    function L(t, r, n, i, o) {
      return e(this, void 0, void 0, function* () {
        yield z.acquire(b, () =>
          e(this, void 0, void 0, function* () {
            let e = (yield O(b)) || [],
              s = (yield O(x)) || [];
            const a = e.findIndex((e) => e.downloadId === t);
            if (-1 !== a) {
              const d = e.splice(a, 1)[0];
              (-1 === s.findIndex((e) => e.downloadId === t) &&
                (s.unshift(
                  Object.assign(Object.assign({}, d), {
                    status: r,
                    downloadMethod: n,
                    msg: i,
                    fileUrl: o,
                    updateTime: new Date().getTime(),
                  }),
                ),
                s.length > A && (s = s.slice(0, A))),
                yield E(x, s));
            }
            yield E(b, e);
          }),
        );
      });
    }
    let M = !1;
    function R(t) {
      return e(this, void 0, void 0, function* () {
        if (!M) {
          M = !0;
          try {
            yield z.acquire(_, () =>
              e(this, void 0, void 0, function* () {
                let e = yield j("download");
                if (0 !== e.length) {
                  let r = yield j("downloading");
                  const n = r.filter(
                      (e) => e.downloadMethod === p.processVideoInWeb,
                    ).length,
                    i = r.filter(
                      (e) => e.downloadMethod === p.processVideo,
                    ).length;
                  if (n < 1) {
                    const r = e.find(
                      (e) => e.downloadMethod === p.processVideoInWeb,
                    );
                    r && t && (yield t(r));
                  }
                  if (i < 1) {
                    const r = e.find(
                      (e) => e.downloadMethod === p.processVideo,
                    );
                    r && t && (yield t(r));
                  }
                }
              }),
            );
          } finally {
            M = !1;
          }
        }
      });
    }
    function P(t) {
      return e(this, void 0, void 0, function* () {
        let e = yield O(x);
        e || (e = []);
        const r = e.findIndex((e) => e.downloadId === t.downloadId);
        (-1 === r
          ? e.unshift(t)
          : (e[r] = Object.assign(Object.assign({}, e[r]), t)),
          yield E(x, e));
      });
    }
    function N(t) {
      return e(this, void 0, void 0, function* () {
        let e = yield O(_);
        e || (e = []);
        const r = e.findIndex((e) => e.downloadId === t);
        -1 !== r && (e.splice(r, 1), yield E(_, e));
      });
    }
    function F(t) {
      return e(this, void 0, void 0, function* () {
        let r = p.processVideoInWeb;
        t.downloadMethod == p.processVideoInWeb && (r = p.processVideo);
        let n = {
          downloadId: t.downloadId,
          courseId: t.courseId,
          lectureId: t.lectureId,
          videoName: t.videoName,
          downloadTime: t.downloadTime,
          secretKey: t.secretKey,
          downloadMethod: r,
          webUrl: t.webUrl,
          status: u.downloadWating,
          msg: "Waiting for download...",
        };
        (yield (function (t) {
          return e(this, void 0, void 0, function* () {
            let e = yield O(_);
            e || (e = []);
            const r = e.findIndex((e) => e.downloadId === t.downloadId);
            (-1 !== r
              ? (e[r] = Object.assign(Object.assign({}, e[r]), t))
              : e.unshift(t),
              yield E(_, e));
          });
        })(n),
          yield (function (t) {
            return e(this, void 0, void 0, function* () {
              let e = yield O(x);
              e || (e = []);
              const r = e.findIndex((e) => e.downloadId === t);
              -1 !== r && (e.splice(r, 1), yield E(x, e));
            });
          })(n.downloadId));
      });
    }
    function j(t) {
      return e(this, void 0, void 0, function* () {
        if ("downloading" === t) return (yield O(b)) || [];
        if ("downloaded" === t) return (yield O(x)) || [];
        if ("download" === t) return (yield O(_)) || [];
        throw new Error(
          'Invalid type specified. Use "downloading" or "downloaded".',
        );
      });
    }
    function W(e) {
      return "udemy.com,udemybusiness,ssudemy.com,udemyfreecourses.org,discudemy.com,premiumm.click,freecourseudemy.com"
        .split(",")
        .some((t) => e.includes(t));
    }
    function Z(t) {
      return e(this, void 0, void 0, function* () {
        let e = {
          title: t.title,
          text: t.content,
          mainText: t.mainText,
          mainAction: "blank",
          mainUrl: t.mainActionUrl,
          subText: t.subText,
          subUrl: null,
          type: a.goSubscribe,
        };
        const r = yield i().tabs.query({});
        for (const t of r) t.url && W(t.url) && i().tabs.sendMessage(t.id, e);
      });
    }
    class $ {
      static userReg(e, t, r, n, i, o) {
        const s = new FormData();
        let a = y();
        for (const e in a) s.append(e, a[e]);
        (s.append("userId", e),
          s.append("extId", t),
          s.append("version", r),
          s.append("action", n),
          s.append("detail", JSON.stringify(i)),
          console.log(
            "fetch url:https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-1",
          ),
          fetch(
            "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-1",
            { method: "POST", body: s },
          ).then((e) => {
            o && o(e);
          }));
      }
      static logAction(t, r, n, i, o, s, a) {
        return e(this, void 0, void 0, function* () {
          console.log("====start log verion:" + n);
          const e = new FormData();
          let d = y();
          for (const t in d) e.append(t, d[t]);
          (e.append("userId", t),
            e.append("extId", r),
            e.append("version", n),
            e.append("action", i),
            e.append("detail", JSON.stringify(o)),
            s && (e.append("url", s), e.append("domain", new URL(s).hostname)));
          let l = null;
          try {
            let t = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/log-action-1",
              { method: "POST", body: e },
            );
            (a && a(t), (l = t.json()));
          } catch (e) {}
          return l;
        });
      }
      static userReg2(t, r, n, i, o, s, a, d) {
        return e(this, void 0, void 0, function* () {
          const e = new FormData();
          let l = y();
          for (const t in l) e.append(t, l[t]);
          (e.append("userId", t),
            e.append("extId", r),
            e.append("version", n),
            e.append("action", i),
            e.append("email", o),
            e.append("password", s),
            e.append("emailCode", a),
            e.append("detail", JSON.stringify(d)));
          try {
            const t = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-2",
              { method: "POST", body: e },
            );
            if (!t.ok) {
              return { success: !1, error: yield t.json() };
            }
            return { success: !0, data: yield t.json() };
          } catch (e) {
            return { success: !1, error: e.message };
          }
        });
      }
      static userLogin(t, r, n, i, o, s, a) {
        return e(this, void 0, void 0, function* () {
          const e = new FormData();
          let d = y();
          for (const t in d) e.append(t, d[t]);
          (e.append("userId", t),
            e.append("extId", r),
            e.append("version", n),
            e.append("action", i),
            e.append("email", o),
            e.append("password", s),
            e.append("detail", JSON.stringify(a)));
          try {
            const t = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/login",
              { method: "POST", body: e },
            );
            if (!t.ok) {
              return { success: !1, error: yield t.json() };
            }
            return { success: !0, data: yield t.json() };
          } catch (e) {
            return { success: !1, error: e.message };
          }
        });
      }
      static sendEmailCode(t, r, n, i, o, s) {
        return e(this, void 0, void 0, function* () {
          const e = new FormData();
          let a = y();
          for (const t in a) e.append(t, a[t]);
          (e.append("userId", t),
            e.append("extId", r),
            e.append("version", n),
            e.append("action", i),
            e.append("email", o),
            e.append("detail", JSON.stringify(s)));
          try {
            const t = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/getEmailCode",
              { method: "POST", body: e },
            );
            if (!t.ok) {
              return { success: !1, error: yield t.json() };
            }
            return { success: !0, data: yield t.json() };
          } catch (e) {
            return { success: !1, error: e.message };
          }
        });
      }
      static getSecretKeyPre(t) {
        return e(this, void 0, void 0, function* () {
          const e = new FormData();
          e.append("pssh", t);
          try {
            const t = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getSecretKeyPre",
              { method: "POST", body: e },
            );
            if (!t.ok) {
              return { success: !1, error: yield t.json() };
            }
            const r = yield t.json();
            return 0 == r.code
              ? { success: !0, data: r.data }
              : { success: !1, error: r.msg };
          } catch (e) {
            return { success: !1, error: e.message };
          }
        });
      }
      static getSecretKeyAfter(t, r, n, i) {
        return e(this, void 0, void 0, function* () {
          const e = new FormData();
          (e.append("session_id", t), e.append("licence", r));
          try {
            const t = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getSecretKeyAfter",
              { method: "POST", body: e },
            );
            if (!t.ok) {
              return { success: !1, error: yield t.json() };
            }
            const r = yield t.json();
            return 0 == r.code
              ? (yield this.saveDrmKey(
                  "a5376339-a50f-f096-248c-0e0cdde4f9af",
                  n,
                  r.data.keys.trim(),
                  i,
                ),
                { success: !0, data: r.data })
              : { success: !1, error: r.msg };
          } catch (e) {
            return { success: !1, error: e.message };
          }
        });
      }
      static checkHasVideo(t, r) {
        return e(this, void 0, void 0, function* () {
          const e = new FormData();
          (e.append("extId", t), e.append("courseId", r));
          try {
            const t = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/file/ude/checkHasVideo",
              { method: "POST", body: e },
            );
            if (!t.ok) {
              return { success: !1, error: yield t.json() };
            }
            return { success: !0, data: yield t.json() };
          } catch (e) {
            return { success: !1, error: e.message };
          }
        });
      }
      static getExtDrmKey(t, r) {
        return e(this, void 0, void 0, function* () {
          const e = new FormData();
          (e.append("extId", t), e.append("courseId", r));
          try {
            const t = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getExtDrmKey",
              { method: "POST", body: e },
            );
            if (!t.ok) {
              return { success: !1, error: yield t.json() };
            }
            return { success: !0, data: yield t.json() };
          } catch (e) {
            return { success: !1, error: e.message };
          }
        });
      }
      static saveDrmKey(t, r, n, i) {
        return e(this, void 0, void 0, function* () {
          const e = new FormData();
          (e.append("extId", t),
            e.append("courseId", r),
            e.append("drmKey", n),
            e.append("userId", i));
          try {
            const t = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/saveDrmKey",
              { method: "POST", body: e },
            );
            if (!t.ok) {
              return { success: !1, error: yield t.json() };
            }
            return { success: !0, data: yield t.json() };
          } catch (e) {
            return { success: !1, error: e.message };
          }
        });
      }
      static getConfig() {
        return e(this, void 0, void 0, function* () {
          let e = "",
            t = !0;
          try {
            const r = new FormData();
            r.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af");
            const n = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/getConfig",
              { method: "POST", body: r },
            );
            if (n.ok) {
              e = (yield n.json()).data;
            } else t = !1;
          } catch (e) {
            ((t = !1), console.log(e));
          }
          return (
            !t && this.retryCount < this.retryMaxCount
              ? (this.retryCount++, yield k(1e3), this.getConfig())
              : (this.retryCount = 0),
            e
          );
        });
      }
      static getUserMemberInfo(t, r) {
        return e(this, void 0, void 0, function* () {
          // Return unlimited access for all users - local activation
          return {
            userId: t || "unlimited_user",
            memberName: "ProMember",
            maxDownloadCount: 999999,
            downloadedCount: 0,
            allDownloadedCount: 0,
            expiredTime: "2099-12-31",
            noPerDayMemberList: [],
            canBusiness: true
          };
        });
      }
      static getDownloadCount(t) {
        return e(this, void 0, void 0, function* () {
          let e = null;
          try {
            const r = new FormData();
            let n = y();
            for (const e in n) r.append(e, n[e]);
            (r.append("userId", t),
              r.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"));
            const i = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/getDownloadCount",
              { method: "POST", body: r },
            );
            if (i.ok) {
              const t = yield i.json();
              0 === t.code && (e = t.data);
            }
          } catch (e) {
            console.log(e);
          }
          return e;
        });
      }
      static updateNoPerDayDownloadCount(t) {
        return e(this, void 0, void 0, function* () {
          // Local update - no server needed, unlimited downloads
          console.log("Local download count update - unlimited downloads");
          return true;
        });
      }
      static downloadRecord(t, r) {
        return e(this, void 0, void 0, function* () {
          try {
            const e = new FormData();
            let n = y();
            for (const t in n) e.append(t, n[t]);
            (e.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"),
              e.append("userId", t),
              e.append("version", i().runtime.getManifest().version));
            let o = JSON.stringify({ perday: r });
            e.append("detail", o);
            const s = yield fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/record/ude/downloadRecord",
              { method: "POST", body: e },
            );
            if (s.ok) {
              0 === (yield s.json()).code &&
                console.log("downloadRecord success");
            }
          } catch (e) {
            console.log(e);
          }
        });
      }
    }
    (($.retryCount = 0), ($.retryMaxCount = 3));
    r(733);
    var V, K;
    (!(function (e) {
      ((e.download = "download"),
        (e.progress = "progress"),
        (e.end = "end"),
        (e.cancle = "cancle"),
        (e.merge = "merge"),
        (e.error = "error"));
    })(V || (V = {})),
      (function (e) {
        ((e.downloadingToDownloaded = "downloadingToDownloaded"),
          (e.downloadToDownloaded = "downloadToDownloaded"));
      })(K || (K = {})));
    function H(t) {
      return e(this, void 0, void 0, function* () {
        let e = yield C();
        const r = yield T();
        let n = {
            downloadId: t.downloadId,
            courseId: t.courseId,
            mediaUrl: t.mediaUrl,
            filenameOutput: t.videoName,
            lectureId: t.lectureId,
            secretKey: t.secretKey,
            userId: e.userId,
            version: J.version,
            downloadSegmentsCount: r,
            webUrl: t.webUrl,
            downloadTime: t.downloadTime,
            downloadMethod: t.downloadMethod,
          },
          o = { action: V.download, workerDownloadMessage: n };
        if (S() == d.Firefox);
        else {
          yield J.setupOffscreenDocument(J.offscreenUrl);
          let e = i().runtime.connect({ name: "popupNotification" });
          (e.postMessage(o),
            setTimeout(() => {
              e.disconnect();
            }, 200));
        }
      });
    }
    function G(t) {
      return e(this, void 0, void 0, function* () {
        let e = t.videoUrlList[0],
          r = yield C(),
          n = t.captionUrlList[0];
        if (
          (n &&
            "" != n &&
            (yield i().downloads.download({
              url: n,
              filename: t.captionNameList[0],
            })),
          "" != e)
        ) {
          let n = t.videoNameList[0];
          (yield i().downloads.download({ url: e, filename: n }),
            yield E("downloadStatus", !0),
            yield D(),
            yield q(t.courseId, t.videoIdList[0], n));
          let o = yield $.logAction(
            r.userId,
            "a5376339-a50f-f096-248c-0e0cdde4f9af",
            J.version,
            c.downloadSignalVideo,
            { videoUrl: e, flag: !0 },
            t.url,
            null,
          );
          if (0 == o.code && null != o.data) {
            let e = o.data;
            yield Z(e);
          }
        } else {
          let n = t.downloadQueueList[0];
          e = n.mediaUrl;
          let o = n.videoName,
            s = yield $.checkHasVideo(
              "a5376339-a50f-f096-248c-0e0cdde4f9af",
              n.lectureId,
            );
          if (s.success && 0 == s.data.code && s.data.data.hasVideo) {
            let n =
              "https://www.bestaddons.store/downloader/udemy/medias/a5376339-a50f-f096-248c-0e0cdde4f9af/" +
              s.data.data.filePath;
            (console.log(n),
              yield i().downloads.download({ url: n, filename: o }),
              yield E("downloadStatus", !0),
              yield D(),
              yield q(t.courseId, t.videoIdList[0], o));
            let a = yield $.logAction(
              r.userId,
              "a5376339-a50f-f096-248c-0e0cdde4f9af",
              J.version,
              c.downloadSignalVideo,
              { videoUrl: e, flag: !0 },
              t.url,
              null,
            );
            if (0 == a.code && null != a.data) {
              let e = a.data;
              yield Z(e);
            }
          } else {
            let e = y(),
              r = {
                downloadId: e.uuid,
                courseId: t.courseId,
                lectureId: n.lectureId,
                videoName: n.videoName,
                downloadTime: e.date,
                secretKey: t.secretKey,
                msg: "Waiting for download...",
                webUrl: n.webUrl,
                mediaUrl: n.mediaUrl,
                status: u.downloadWating,
                downloadMethod: p.processVideoInWeb,
              };
            yield U(r);
          }
        }
      });
    }
    function q(t, r, n) {
      return e(this, void 0, void 0, function* () {
        let e = y(),
          i = {
            downloadId: e.uuid,
            courseId: t,
            lectureId: r,
            videoName: n,
            downloadTime: e.date,
            secretKey: "",
            webUrl: "",
            downloadMethod: p.processVideoByUrl,
            status: u.downloadSuccess,
            msg: "",
            fileUrl: "",
          };
        (yield P(i), J.sendSidepanelsNotification("", m.downloaded));
      });
    }
    function Q() {
      return e(this, void 0, void 0, function* () {
        const t = { success: !0, msg: "" };
        try {
          let r = yield O("googleLoginConfig");
          if (r) {
            let n = JSON.parse(r.toString()),
              o = "";
            ((o =
              S() == d.Firefox
                ? i().identity.getRedirectURL()
                : n.redirect_uri.replace(
                    "{browser.runtime.id}",
                    i().runtime.id,
                  )),
              console.log(o));
            let s =
              "https://accounts.google.com/o/oauth2/v2/auth?response_type=token&client_id=" +
              n.client_id +
              "&scope=" +
              encodeURIComponent(n.scopes.join(" ")) +
              "&redirect_uri=" +
              o;
            console.log(s);
            const a = yield i().identity.launchWebAuthFlow({
              url: s,
              interactive: !0,
            });
            if (i().runtime.lastError)
              (console.log(i().runtime.lastError),
                (t.success = !1),
                (t.msg = i().runtime.lastError.message));
            else {
              console.log(a);
              const r = (function (e) {
                try {
                  const t = new URL(e).hash.substring(1),
                    r = new URLSearchParams(t);
                  return r.get("access_token");
                } catch (e) {
                  return (console.error("Invalid URL", e), null);
                }
              })(a);
              if (r) {
                const n = yield (function (t) {
                  return e(this, void 0, void 0, function* () {
                    try {
                      const e = yield fetch(
                        "https://www.googleapis.com/oauth2/v3/userinfo",
                        { headers: { Authorization: "Bearer " + t } },
                      );
                      if (!e.ok) {
                        return { success: !1, error: yield e.json() };
                      }
                      return { success: !0, data: yield e.json() };
                    } catch (e) {
                      return { success: !1, error: e.message };
                    }
                  });
                })(r);
                n.success
                  ? (console.log(n), (t.success = !0), (t.data = n.data))
                  : ((t.success = !1), (t.msg = n.error));
              } else
                ((t.success = !1),
                  (t.msg = "Failed to get access token from redirect URL."),
                  console.error(
                    "Failed to get access token from redirect URL.",
                  ));
            }
          }
        } catch (e) {
          (console.log(e), (t.success = !1), (t.msg = "google login fail"));
        }
        return t;
      });
    }
    function Y(t) {
      return e(this, void 0, void 0, function* () {
        let e = yield C(),
          r = t.loginOrRegisterInfo,
          n = c.login;
        return (
          t.type == s.googleLogin && (n = c.googleLogin),
          yield $.userLogin(
            e.userId,
            "a5376339-a50f-f096-248c-0e0cdde4f9af",
            i().runtime.getManifest().version,
            n,
            r.email,
            r.password,
            {},
          )
        );
      });
    }
    var X;
    let J = (X = class {
      constructor() {
        ((X.version = i().runtime.getManifest().version),
          console.log(i().runtime),
          i().runtime.onInstalled.addListener(X.onUpdate),
          i().runtime.onMessage.addListener(X.onMessage),
          i().runtime.onConnect.addListener(X.onPortMessage),
          i().tabs.onUpdated.addListener((t, r) =>
            e(this, void 0, void 0, function* () {
              (console.log(r), (X.tabId = t));
              const e = yield i().tabs.get(t),
                n = S() == d.Firefox;
              e.url && W(e.url)
                ? n
                  ? i().sidebarAction.setPanel({
                      tabId: t,
                      panel: X.sidepanelsUrl,
                    })
                  : yield i().sidePanel.setOptions({
                      tabId: t,
                      path: X.sidepanelsUrl,
                      enabled: !0,
                    })
                : n
                  ? i().sidebarAction.setPanel({})
                  : yield i().sidePanel.setOptions({ tabId: t, enabled: !1 });
            }),
          ),
          console.log("BackgroundMessageHandler.version:" + X.version),
          i().contextMenus.onClicked.addListener((e, t) => {
            "UdemyFetcher™" === e.menuItemId &&
              t.url &&
              W(t.url) &&
              X.openSidePanel(t.id);
          }));
        const t = S() == d.Firefox;
        (i().storage.onChanged.addListener((r, n) =>
          e(this, void 0, void 0, function* () {
            if ("local" === n) {
              if (r[b]) {
                X.sendSidepanelsNotification("", m.downloading);
                const e = r[b].newValue;
                yield X.startDownloadToDownloading();
                const n = (yield O(_)) || [];
                (null != e && 0 != e.length) ||
                  (null != n && 0 != n.length) ||
                  (!t &&
                    i().offscreen.hasDocument &&
                    i().offscreen.closeDocument());
              }
              if (r[_]) {
                const e = r[_].newValue;
                if (e && e.length > 0)
                  try {
                    (t || (yield X.setupOffscreenDocument(X.offscreenUrl)),
                      yield R(H));
                  } catch (e) {}
                else if (!t) {
                  const e = (yield O(b)) || [];
                  (null != e && 0 != e.length) ||
                    !i().offscreen.hasDocument ||
                    i().offscreen.closeDocument();
                }
                X.sendSidepanelsNotification("", m.download);
              }
              (r[x] && X.sendSidepanelsNotification("", m.downloaded),
                r.maxConcurrentDownloads &&
                  (yield X.startDownloadToDownloading()));
            }
          }),
        ),
          t &&
            i().commands.onCommand.addListener((e) => {
              "open-download-sidebar" === e && X.openSidePanel(X.tabId);
            }),
          i().webRequest.onBeforeRequest.addListener(
            (t) =>
              e(this, void 0, void 0, function* () {
                if ("POST" === t.method && -1 == t.url.indexOf("ext=abc")) {
                  let e = {
                    title: "",
                    text: "",
                    mainText: "",
                    mainUrl:
                      t.url.indexOf("?") > -1
                        ? t.url + "&ext=abc"
                        : t.url + "?ext=abc",
                    subText: "",
                    subUrl: "",
                    type: a.drmLicense,
                  };
                  i()
                    .tabs.sendMessage(t.tabId, e)
                    .then((e) => {
                      console.log("Response from content script:", e);
                    });
                }
              }),
            { urls: ["https://*/media-license-server/*"] },
            ["requestBody"],
          ),
          i().alarms.create("getConfig", { periodInMinutes: parseInt("5") }),
          i().alarms.onAlarm.addListener((t) =>
            e(this, void 0, void 0, function* () {
              if ("getConfig" === t.name) {
                const e = yield $.getConfig();
                (X.setUninstallUrl(e), X.initConfig(e));
              }
            }),
          ),
          X.onStartup());
        const r = () => {
          (X.checkStuck(), setTimeout(r, 5e3));
        };
        r();
      }
      static startDownloadToDownloading() {
        return e(this, void 0, void 0, function* () {
          const e = (yield O(_)) || [];
          if (e && e.length > 0)
            try {
              (S() == d.Firefox ||
                (yield X.setupOffscreenDocument(X.offscreenUrl)),
                yield R(H));
            } catch (e) {}
        });
      }
      static initConfig(e) {
        (X.setDownloadLimit(e),
          X.getDiscordUrl(e),
          X.getEnableRecordUrl(e),
          X.getDownloadSegmentsCount(e),
          X.getGoogleLoginConfig(e),
          X.getConcurrentDownloadsConfig(e),
          X.getStuckActivationSeconds(e));
      }
      static getUserMemberInfo() {
        return e(this, void 0, void 0, function* () {
          let e = yield C();
          const t = yield $.getUserMemberInfo(e.userId, X.version);
          t
            ? ((e.noPerDayMemberList = t.noPerDayMemberList),
              (e.memberName = t.memberName),
              (e.maxDownloadCount = t.maxDownloadCount),
              (e.downloadedCount =
                null == t.downloadedCount ? 0 : t.downloadedCount),
              (e.allDownloadedCount =
                null == t.allDownloadedCount ? 0 : t.allDownloadedCount),
              (e.expiredTime = t.expiredTime),
              (e.canBusiness = t.canBusiness),
              yield E("UdemyDownloaderUserInfo", e),
              yield E("downloadCount", e.downloadedCount))
            : yield E("downloadCount", -2);
        });
      }
      static getServerDownloadCount() {
        return e(this, void 0, void 0, function* () {
          let e = yield C();
          const t = yield $.getDownloadCount(e.userId);
          null != t && (yield E("serverDownloadCount", t));
        });
      }
      static setDownloadLimit(t) {
        return e(this, void 0, void 0, function* () {
          t && "" != t && t.downloadLimitConfig
            ? yield E(
                "downloadLimit",
                parseInt(t.downloadLimitConfig.toString()),
              )
            : yield E("downloadLimit", parseInt("2"));
        });
      }
      static setUninstallUrl(t) {
        return e(this, void 0, void 0, function* () {
          let e = {};
          if (
            (t &&
              "" != t &&
              t.installConfig &&
              (e = JSON.parse(t.installConfig.toString())),
            e && e.uninstallUrl)
          ) {
            let t = yield I();
            i().runtime.setUninstallURL(
              e.uninstallUrl +
                "?userId=" +
                t.userId +
                "&version=" +
                X.version +
                "&extId=a5376339-a50f-f096-248c-0e0cdde4f9af",
            );
          }
        });
      }
      static getDiscordUrl(t) {
        return e(this, void 0, void 0, function* () {
          t && "" != t && t.discordUrlConfig
            ? yield E("discordUrl", t.discordUrlConfig.toString())
            : yield E("discordUrl", "https://discord.gg/xwNpztJbGt");
        });
      }
      static getDownloadSegmentsCount(t) {
        return e(this, void 0, void 0, function* () {
          t && "" != t && t.downloadSegmentsCountConfig
            ? yield E(
                "downloadSegmentsCount",
                parseInt(t.downloadSegmentsCountConfig.toString()),
              )
            : yield E("downloadSegmentsCount", parseInt("15"));
        });
      }
      static getEnableRecordUrl(t) {
        return e(this, void 0, void 0, function* () {
          t && "" != t && t.enableRecordUrlConfig
            ? yield E("enableRecordUrl", t.enableRecordUrlConfig.toString())
            : yield E("enableRecordUrl", "false");
        });
      }
      static getGoogleLoginConfig(t) {
        return e(this, void 0, void 0, function* () {
          t &&
            "" != t &&
            t.googleLoginConfig &&
            (yield E("googleLoginConfig", t.googleLoginConfig.toString()));
        });
      }
      static getConcurrentDownloadsConfig(t) {
        return e(this, void 0, void 0, function* () {
          let e = {};
          (t &&
            "" != t &&
            t.concurrentDownloadsConfig &&
            (e = JSON.parse(t.concurrentDownloadsConfig.toString())),
            e &&
              (e.concurrentDownloadsOption
                ? yield E(
                    "concurrentDownloadsOption",
                    e.concurrentDownloadsOption,
                  )
                : yield E("concurrentDownloadsOption", "1,2,3"),
              e.maxConcurrentDownloads
                ? yield E(
                    "maxConcurrentDownloads",
                    parseInt(e.maxConcurrentDownloads.toString()),
                  )
                : yield E("maxConcurrentDownloads", parseInt("1"))));
        });
      }
      static getStuckActivationSeconds(t) {
        return e(this, void 0, void 0, function* () {
          t &&
            "" != t &&
            t.stuckActivationSeconds &&
            (yield E(
              "stuckActivationSeconds",
              parseInt(t.stuckActivationSeconds.toString()),
            ));
        });
      }
      static sendMessageAndDisconnect(t, r) {
        return e(this, void 0, void 0, function* () {
          return (
            t.postMessage(r),
            new Promise((e) => {
              setTimeout(() => {
                (t.disconnect(), e());
              }, 200);
            })
          );
        });
      }
      static checkStuck() {
        return e(this, void 0, void 0, function* () {
          try {
            let t = (yield O(b)) || [];
            if (
              ((t = t.filter((e) => e.downloadMethod == p.processVideoInWeb)),
              t && t.length > 0)
            ) {
              let r = yield O("stuckActivationSeconds");
              (null == r && (r = parseInt("60")),
                t.forEach((t) =>
                  e(this, void 0, void 0, function* () {
                    if (t.updateTime && t.percent < 98) {
                      let e = new Date().getTime() - t.updateTime;
                      ((e /= 1e3),
                        e >= r &&
                          ((t.status = u.downloadStuck),
                          yield B(t),
                          X.sendSidepanelsNotification("", m.downloading)));
                    }
                  }),
                ));
            }
          } catch (e) {
            console.log(e);
          }
        });
      }
      static onPortMessage(t) {
        return e(this, void 0, void 0, function* () {
          "openSidepanels" === t.name
            ? t.hasSidepanelsListener ||
              ((t.hasSidepanelsListener = !0),
              t.onMessage.addListener(X.onMessageConnect))
            : "downloadProgress" === t.name
              ? t.hasDownloadProgressListener ||
                ((t.hasDownloadProgressListener = !0),
                t.onMessage.addListener((t) =>
                  e(this, void 0, void 0, function* () {
                    let e = t.data;
                    try {
                      ((e.updateTime = new Date().getTime()),
                        t.action === f.addOrUpdateDownloadingInfo
                          ? (e = yield B(e))
                          : t.action === f.updateDownloadStatus &&
                            (yield L(
                              e.downloadId,
                              t.dowloadStatus,
                              e.downloadMethod,
                              t.msg,
                              e.fileUrl,
                            )));
                    } catch (t) {
                      (console.log(t),
                        e.downloadId &&
                          (yield L(
                            e.downloadId,
                            u.downloadError,
                            t.toString(),
                          )));
                    }
                  }),
                ))
              : "downloadFile" === t.name
                ? t.hasDownloadFileListener ||
                  ((t.hasDownloadFileListener = !0),
                  t.onMessage.addListener((t) =>
                    e(this, void 0, void 0, function* () {
                      const e = t.flag;
                      e &&
                        (i().downloads.download({
                          url: t.url,
                          filename: t.filenameOutput,
                        }),
                        yield D());
                      let r = yield C(),
                        n = yield $.logAction(
                          r.userId,
                          "a5376339-a50f-f096-248c-0e0cdde4f9af",
                          X.version,
                          c.downloadSignalVideo,
                          {
                            videoUrl: t.mediaUrl,
                            flag: e,
                            errorMsg: t.errorMsg,
                          },
                          null,
                          null,
                        );
                      if (0 == n.code && null != n.data) {
                        let e = n.data;
                        yield Z(e);
                      }
                    }),
                  ))
                : "cancleDownloading" === t.name
                  ? t.hasCancleDownloadingListener ||
                    ((t.hasCancleDownloadingListener = !0),
                    t.onMessage.addListener((t) =>
                      e(this, void 0, void 0, function* () {
                        if (S() == d.Firefox) {
                          let e = yield C();
                          yield $.logAction(
                            e.userId,
                            "a5376339-a50f-f096-248c-0e0cdde4f9af",
                            X.version,
                            c.downloadSignalVideo,
                            {
                              videoUrl: t.workerDownloadMessage.mediaUrl,
                              flag: !1,
                              errorMsg: "user cancle",
                            },
                            null,
                            null,
                          );
                        } else {
                          yield X.setupOffscreenDocument(X.offscreenUrl);
                          let e = i().runtime.connect({
                            name: "popupNotification",
                          });
                          (e.postMessage(t), e.disconnect());
                        }
                      }),
                    ))
                  : "updateDownloadStatus" === t.name
                    ? t.hasUpdateStatusListener ||
                      ((t.hasUpdateStatusListener = !0),
                      t.onMessage.addListener((t) =>
                        e(this, void 0, void 0, function* () {
                          const r = t.workerDownloadMessage;
                          (yield L(
                            r.downloadId,
                            t.status,
                            r.downloadMethod,
                            t.errorMsg,
                            null,
                          ),
                            t.hjname == K.downloadToDownloaded &&
                              (yield (function (t, r, n) {
                                return e(this, void 0, void 0, function* () {
                                  yield N(t.downloadId);
                                  let e = {
                                    downloadId: t.downloadId,
                                    courseId: t.courseId,
                                    lectureId: t.lectureId,
                                    videoName: t.filenameOutput,
                                    downloadTime: t.downloadTime,
                                    secretKey: t.secretKey,
                                    webUrl: t.webUrl,
                                    downloadMethod: t.downloadMethod,
                                    status: r,
                                    msg: n,
                                    fileUrl: "",
                                  };
                                  yield P(e);
                                });
                              })(r, t.status, t.errorMsg)));
                          let n = yield C();
                          yield $.logAction(
                            n.userId,
                            "a5376339-a50f-f096-248c-0e0cdde4f9af",
                            X.version,
                            c.downloadSignalVideo,
                            {
                              videoUrl: r.mediaUrl,
                              flag: !1,
                              errorMsg: t.errorMsg,
                            },
                            null,
                            null,
                          );
                        }),
                      ))
                    : "retryDownload" === t.name
                      ? t.hasRetryDownloadListener ||
                        ((t.hasRetryDownloadListener = !0),
                        t.onMessage.addListener((t) =>
                          e(this, void 0, void 0, function* () {
                            yield F(t);
                          }),
                        ))
                      : "errorRetryDownload" === t.name
                        ? t.hasErrorRetryDownloadListener ||
                          ((t.hasErrorRetryDownloadListener = !0),
                          t.onMessage.addListener((t) =>
                            e(this, void 0, void 0, function* () {
                              (yield L(
                                t.downloadId,
                                u.downloadError,
                                t.downloadMethod,
                                t.msg,
                                t.fileUrl,
                              ),
                                yield F(t));
                            }),
                          ))
                        : "downloadFileInWeb" === t.name
                          ? t.hasDownloadFileInWebListener ||
                            ((t.hasDownloadFileInWebListener = !0),
                            t.onMessage.addListener((t) =>
                              e(this, void 0, void 0, function* () {
                                const e = t.flag;
                                e && (yield D());
                                let r = yield C(),
                                  n = yield $.logAction(
                                    r.userId,
                                    "a5376339-a50f-f096-248c-0e0cdde4f9af",
                                    X.version,
                                    c.downloadSignalVideo,
                                    {
                                      videoUrl: t.mediaUrl,
                                      flag: e,
                                      errorMsg: t.errorMsg,
                                    },
                                    null,
                                    null,
                                  );
                                if (0 == n.code && null != n.data) {
                                  let e = n.data;
                                  yield Z(e);
                                }
                              }),
                            ))
                          : "retryMessage" === t.name
                            ? t.hasRetryMessageListener ||
                              ((t.hasRetryMessageListener = !0),
                              t.onMessage.addListener((t) =>
                                e(this, void 0, void 0, function* () {
                                  let e = a.retryMessage;
                                  t.type == g.serverError &&
                                    (e = a.serverError);
                                  let r = {
                                    title: "",
                                    text: "",
                                    mainText: "",
                                    mainAction: "blank",
                                    mainUrl: "",
                                    subText: "",
                                    subUrl: null,
                                    type: e,
                                  };
                                  const n = yield i().tabs.query({});
                                  for (const e of n)
                                    e.url &&
                                      W(e.url) &&
                                      i().tabs.sendMessage(e.id, r);
                                }),
                              ))
                            : "startStuck" === t.name &&
                              (t.hasStartStuckListener ||
                                ((t.hasStartStuckListener = !0),
                                t.onMessage.addListener((t) =>
                                  e(this, void 0, void 0, function* () {
                                    X.startStuckDownloading(t);
                                  }),
                                )));
        });
      }
      static createContextMenu() {
        let e = "UdemyFetcher™",
          t =
            "*://*.udemy.com/*,*://*.udemybusiness.click/*,*://*.ssudemy.com/*,*://*.udemyfreecourses.org/*,*://*.discudemy.com/*,*://*.premiumm.click/*,*://*.freecourseudemy.com/*".split(
              ",",
            );
        i().contextMenus.create({
          id: e,
          title: e,
          contexts: ["all"],
          documentUrlPatterns: t,
        });
      }
      static openSidePanel(e) {
        S() == d.Firefox
          ? (i().sidebarAction.setPanel({ tabId: e, panel: X.sidepanelsUrl }),
            i()
              .sidebarAction.open()
              .then(() => {
                E("hasOpenSider", !0);
              }))
          : i()
              .sidePanel.open({ tabId: e })
              .then(() => {
                E("hasOpenSider", !0);
              });
      }
      static onStartup() {
        return e(this, void 0, void 0, function* () {
          console.log("llq start......");
          let e = (yield O(b)) || [];
          for (let t = 0; t < e.length; t++) {
            let r = e[t];
            ((r.downloadMethod = p.processVideoInWeb),
              (r.msg = "Abnormal closure"),
              yield L(
                r.downloadId,
                u.downloadError,
                r.downloadMethod,
                r.msg,
                r.fileUrl,
              ),
              yield F(r));
          }
        });
      }
      static onUpdate(t) {
        return e(this, void 0, void 0, function* () {
          X.createContextMenu();
          const r = yield $.getConfig();
          X.initConfig(r);
          let n = {};
          r &&
            "" != r &&
            r.installConfig &&
            (n = JSON.parse(r.installConfig.toString()));
          let o = yield I();
          ("install" == t.reason &&
            $.userReg(
              o.userId,
              "a5376339-a50f-f096-248c-0e0cdde4f9af",
              X.version,
              c.install,
              null,
              () =>
                e(this, void 0, void 0, function* () {
                  yield $.logAction(
                    o.userId,
                    "a5376339-a50f-f096-248c-0e0cdde4f9af",
                    X.version,
                    c.install,
                    null,
                    null,
                    () => {
                      n && n.uninstallUrl
                        ? i().tabs.create({
                            url:
                              n.installUrl +
                              "?userId=" +
                              o.userId +
                              "&version=" +
                              X.version +
                              "&extId=a5376339-a50f-f096-248c-0e0cdde4f9af",
                          })
                        : i().tabs.create({
                            url:
                              "https://www.bestaddons.store/ytbdserver/downloader/webtransfer/ude/user-install-guide?userId=" +
                              o.userId +
                              "&version=" +
                              X.version +
                              "&extId=a5376339-a50f-f096-248c-0e0cdde4f9af",
                          });
                    },
                  );
                }),
            ),
            n && n.uninstallUrl
              ? i().runtime.setUninstallURL(
                  n.uninstallUrl +
                    "?userId=" +
                    o.userId +
                    "&version=" +
                    X.version +
                    "&extId=a5376339-a50f-f096-248c-0e0cdde4f9af",
                )
              : i().runtime.setUninstallURL(
                  "https://www.bestaddons.store/ytbdserver/downloader/webtransfer/ude/user-uninstall-guide?userId=" +
                    o.userId +
                    "&version=" +
                    X.version +
                    "&extId=a5376339-a50f-f096-248c-0e0cdde4f9af",
                ),
            i()
              .tabs.query({})
              .then((e) => {
                e.forEach((e) => {
                  e.url && W(e.url) && i().tabs.reload(e.id);
                });
              }));
        });
      }
      static onMessage(t) {
        return e(this, void 0, void 0, function* () {
          if (t.type === s.single) return yield G(t);
          if (t.type === s.bulk)
            return yield (function (t, r) {
              return e(this, void 0, void 0, function* () {
                console.info(r);
                let e = t.videoUrlList,
                  n = t.videoNameList;
                for (const [r, o] of e.entries())
                  (yield i().downloads.download({ url: o, filename: n[r] }),
                    yield D(),
                    yield q(t.courseId, t.videoIdList[r], n[r]));
                let o = yield C();
                for (const e of t.downloadQueueList) {
                  let r = yield $.checkHasVideo(
                    "a5376339-a50f-f096-248c-0e0cdde4f9af",
                    e.lectureId,
                  );
                  if (r.success && 0 == r.data.code && r.data.data.hasVideo) {
                    let n =
                      "https://www.bestaddons.store/downloader/udemy/medias/a5376339-a50f-f096-248c-0e0cdde4f9af/" +
                      r.data.data.filePath;
                    (yield i().downloads.download({
                      url: n,
                      filename: e.videoName,
                    }),
                      yield D(),
                      yield q(t.courseId, e.lectureId, e.videoName));
                  } else {
                    let r = y(),
                      n = {
                        downloadId: r.uuid,
                        courseId: t.courseId,
                        lectureId: e.lectureId,
                        videoName: e.videoName,
                        downloadTime: r.date,
                        secretKey: t.secretKey,
                        msg: "Waiting for download...",
                        webUrl: e.webUrl,
                        mediaUrl: e.mediaUrl,
                        status: u.downloadWating,
                        downloadMethod: p.processVideoInWeb,
                      };
                    (yield k(200), yield U(n));
                  }
                }
                $.logAction(
                  o.userId,
                  "a5376339-a50f-f096-248c-0e0cdde4f9af",
                  J.version,
                  c.downloadBulk,
                  { courseId: t.courseId, secretKey: t.secretKey },
                  t.url,
                  null,
                );
              });
            })(t, t.courseName);
          if (t.type === s.bloburl) return yield G(t);
          if (t.type === s.changeUrl)
            return (
              yield (function (t) {
                return e(this, void 0, void 0, function* () {
                  let e = yield C();
                  $.logAction(
                    e.userId,
                    "a5376339-a50f-f096-248c-0e0cdde4f9af",
                    J.version,
                    c.changeUrl,
                    { url: t.url },
                    t.url,
                    null,
                  );
                });
              })(t),
              null
            );
          if (t.type === s.googleLogin) {
            let e = yield Q();
            if (e.success) {
              console.info(e.data);
              let r = { email: e.data.email, password: "", emailCode: "" };
              return ((t.loginOrRegisterInfo = r), yield Y(t));
            }
            return e;
          }
          if (t.type === s.login) return yield Y(t);
          if (t.type === s.register)
            return yield (function (t) {
              return e(this, void 0, void 0, function* () {
                let e = yield C(),
                  r = t.loginOrRegisterInfo;
                return yield $.userReg2(
                  e.userId,
                  "a5376339-a50f-f096-248c-0e0cdde4f9af",
                  i().runtime.getManifest().version,
                  c.register,
                  r.email,
                  r.password,
                  r.emailCode,
                  {},
                );
              });
            })(t);
          if (t.type === s.sendEmailCode) {
            let r = yield (function (t) {
              return e(this, void 0, void 0, function* () {
                let e = yield C(),
                  r = t.loginOrRegisterInfo;
                return yield $.sendEmailCode(
                  e.userId,
                  "a5376339-a50f-f096-248c-0e0cdde4f9af",
                  i().runtime.getManifest().version,
                  c.sendEmailCode,
                  r.email,
                  {},
                );
              });
            })(t);
            return (console.info(r), r);
          }
          if (t.type === s.getDrmSecretKey) {
            let e = t.drmMessage;
            if (e.stage === o.pre) return yield $.getSecretKeyPre(e.pssh);
            if (e.stage === o.after) {
              let r = yield C();
              return yield $.getSecretKeyAfter(
                e.session_id,
                e.content,
                t.courseId,
                r.userId,
              );
            }
            if (e.stage === o.getExtDrmKey)
              return yield $.getExtDrmKey(
                "a5376339-a50f-f096-248c-0e0cdde4f9af",
                t.courseId,
              );
          } else {
            if (t.type === s.getMemberInfo)
              return (yield X.getUserMemberInfo(), null);
            if (t.type === s.updateNoPerDayDownloadCount)
              return (yield $.updateNoPerDayDownloadCount(t.details), null);
          }
        });
      }
      static onMessageConnect(e) {
        console.log(e);
        (S(), d.Firefox);
        let t = X.tabId;
        (console.log(t), X.openSidePanel(t));
      }
      sendMessage(t, r = "*://*.udemy.com/*") {
        return e(this, void 0, void 0, function* () {
          const e = yield i().tabs.query({ url: r });
          for (const r of e) r.id && i().tabs.sendMessage(r.id, t);
        });
      }
      static setupOffscreenDocument(t) {
        return e(this, void 0, void 0, function* () {
          const e = i().runtime.getURL(t);
          0 ==
            (yield i().runtime.getContexts({
              contextTypes: [i().runtime.ContextType.OFFSCREEN_DOCUMENT],
              documentUrls: [e],
            })).length &&
            (this.creating
              ? yield this.creating
              : ((this.creating = i().offscreen.createDocument({
                  url: t,
                  reasons: [
                    i().offscreen.Reason.BLOBS,
                    i().offscreen.Reason.WORKERS,
                  ],
                  justification: "use webWorker",
                })),
                yield this.creating,
                (this.creating = null)));
        });
      }
      static sendSidepanelsNotification(e, t) {
        let r = i().runtime.connect({ name: "sidepanelsNotification" }),
          n = { downloadId: e, action: h.refresh, refreshType: t };
        r && (r.postMessage(n), r.disconnect());
      }
      static startStuckDownloading(t) {
        return e(this, void 0, void 0, function* () {
          let e = yield C();
          const r = yield T();
          let n = {
              downloadId: t.downloadId,
              courseId: t.courseId,
              mediaUrl: "",
              filenameOutput: t.videoName,
              lectureId: t.lectureId,
              secretKey: t.secretKey,
              userId: e.userId,
              version: X.version,
              downloadSegmentsCount: r,
              webUrl: t.webUrl,
              downloadTime: t.downloadTime,
              downloadMethod: p.processVideo,
            },
            o = { action: V.cancle, workerDownloadMessage: n },
            s = i().runtime.connect({ name: "popupNotification" });
          (yield X.sendMessageAndDisconnect(s, o),
            (t.downloadMethod = p.processVideoInWeb),
            yield F(t));
        });
      }
    });
    ((J.version = ""),
      (J.offscreenUrl = "offscreen.html"),
      (J.sidepanelsUrl = "downloadsp.html"),
      (J.tabId = null),
      (J = X =
        (function (e, t, r, n) {
          var i,
            o = arguments.length,
            s =
              o < 3
                ? t
                : null === n
                  ? (n = Object.getOwnPropertyDescriptor(t, r))
                  : n;
          if (
            "object" == typeof Reflect &&
            "function" == typeof Reflect.decorate
          )
            s = Reflect.decorate(e, t, r, n);
          else
            for (var a = e.length - 1; a >= 0; a--)
              (i = e[a]) &&
                (s = (o < 3 ? i(s) : o > 3 ? i(t, r, s) : i(t, r)) || s);
          return (o > 3 && s && Object.defineProperty(t, r, s), s);
        })(
          [
            function (e) {
              return new Proxy(e, {
                construct: (e, t, r) =>
                  e.prototype !== r.prototype
                    ? Reflect.construct(e, t, r)
                    : (e.SINGLETON_INSTANCE ||
                        (e.SINGLETON_INSTANCE = Reflect.construct(e, t, r)),
                      e.SINGLETON_INSTANCE),
              });
            },
          ],
          J,
        )));
    new J();
  })();
})();
