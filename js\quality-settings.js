// إعدادات جودة الفيديو المحسنة
(function() {
    'use strict';
    
    // إعدادات الجودة الافتراضية
    const QualitySettings = {
        // تفضيلات الجودة (من الأعلى للأقل)
        preferredQualities: ['1080p', '720p', '480p', '360p', '240p'],
        
        // الحد الأدنى للجودة المقبولة
        minimumQuality: 480,
        
        // تفضيل تنسيق الملف
        preferredFormats: ['mp4', 'webm', 'avi'],
        
        // حفظ الإعدادات
        saveSettings: function(settings) {
            try {
                localStorage.setItem('udemy_quality_settings', JSON.stringify(settings));
                console.log('Quality settings saved:', settings);
            } catch (e) {
                console.error('Failed to save quality settings:', e);
            }
        },
        
        // تحميل الإعدادات
        loadSettings: function() {
            try {
                const saved = localStorage.getItem('udemy_quality_settings');
                if (saved) {
                    const settings = JSON.parse(saved);
                    Object.assign(this, settings);
                    console.log('Quality settings loaded:', settings);
                }
            } catch (e) {
                console.error('Failed to load quality settings:', e);
            }
        },
        
        // اختيار أفضل جودة محسن
        selectBestQuality: function(videoStreams) {
            if (!videoStreams || videoStreams.length === 0) {
                console.warn('No video streams available');
                return null;
            }
            
            if (videoStreams.length === 1) {
                console.log('Only one video stream available:', videoStreams[0]);
                return videoStreams[0];
            }
            
            console.log('Available video streams:', videoStreams);
            
            let bestVideo = null;
            let maxScore = -1;
            
            videoStreams.forEach((video, index) => {
                let score = 0;
                let qualityInfo = {
                    index: index,
                    label: video.label || 'Unknown',
                    file: video.file || '',
                    score: 0
                };
                
                // تحليل الجودة من label
                if (video.label) {
                    const resolution = parseInt(video.label.replace(/[^\d]/g, '')) || 0;
                    qualityInfo.resolution = resolution;
                    
                    // نقاط الدقة
                    if (resolution >= 1080) score += 1000;
                    else if (resolution >= 720) score += 800;
                    else if (resolution >= 480) score += 600;
                    else if (resolution >= 360) score += 400;
                    else if (resolution >= 240) score += 200;
                    
                    // تطبيق الحد الأدنى للجودة
                    if (resolution < this.minimumQuality) {
                        score -= 500;
                    }
                }
                
                // تحليل الجودة من URL
                if (video.file) {
                    const fileUrl = video.file.toLowerCase();
                    
                    // البحث عن مؤشرات الجودة في URL
                    if (fileUrl.includes('1080')) score += 900;
                    else if (fileUrl.includes('720')) score += 700;
                    else if (fileUrl.includes('480')) score += 500;
                    else if (fileUrl.includes('360')) score += 300;
                    
                    // تفضيل التنسيقات
                    if (fileUrl.includes('.mp4')) score += 100;
                    else if (fileUrl.includes('.webm')) score += 80;
                    else if (fileUrl.includes('.avi')) score += 60;
                    
                    // تفضيل الملفات الأكبر (مؤشر على جودة أفضل)
                    if (fileUrl.includes('high')) score += 50;
                    if (fileUrl.includes('hd')) score += 50;
                    if (fileUrl.includes('quality')) score += 30;
                }
                
                qualityInfo.score = score;
                console.log(`Video ${index} analysis:`, qualityInfo);
                
                if (score > maxScore) {
                    maxScore = score;
                    bestVideo = video;
                }
            });
            
            if (bestVideo) {
                console.log(`✅ Selected best video quality: ${bestVideo.label || 'Unknown'} (Score: ${maxScore})`);
                console.log(`📁 File URL: ${bestVideo.file}`);
            } else {
                console.warn('❌ No suitable video quality found, using first available');
                bestVideo = videoStreams[0];
            }
            
            return bestVideo;
        },
        
        // إنشاء واجهة إعدادات الجودة
        createQualityUI: function() {
            // التحقق من وجود الواجهة مسبقاً
            if (document.getElementById('quality-settings-panel')) {
                return;
            }
            
            const panel = document.createElement('div');
            panel.id = 'quality-settings-panel';
            panel.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                width: 300px;
                background: rgba(30, 30, 30, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 12px;
                padding: 20px;
                color: white;
                font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                z-index: 999999;
                border: 1px solid rgba(255,255,255,0.1);
                display: none;
            `;
            
            panel.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3 style="margin: 0; font-size: 16px;">⚙️ Quality Settings</h3>
                    <button id="close-quality-settings" style="background: rgba(255,255,255,0.2); border: none; color: white; width: 24px; height: 24px; border-radius: 50%; cursor: pointer;">×</button>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 14px;">Minimum Quality:</label>
                    <select id="minimum-quality" style="width: 100%; padding: 8px; border-radius: 6px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white;">
                        <option value="240">240p</option>
                        <option value="360">360p</option>
                        <option value="480">480p</option>
                        <option value="720">720p</option>
                        <option value="1080">1080p</option>
                    </select>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label style="display: flex; align-items: center; gap: 8px; font-size: 14px;">
                        <input type="checkbox" id="auto-select-best" style="accent-color: #4ecdc4;">
                        Always select highest quality
                    </label>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label style="display: flex; align-items: center; gap: 8px; font-size: 14px;">
                        <input type="checkbox" id="show-quality-info" style="accent-color: #4ecdc4;">
                        Show quality info in console
                    </label>
                </div>
                
                <button id="save-quality-settings" style="width: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; color: white; padding: 10px; border-radius: 6px; cursor: pointer; font-weight: 600;">
                    Save Settings
                </button>
            `;
            
            document.body.appendChild(panel);
            
            // تحميل الإعدادات الحالية
            document.getElementById('minimum-quality').value = this.minimumQuality;
            document.getElementById('auto-select-best').checked = this.autoSelectBest !== false;
            document.getElementById('show-quality-info').checked = this.showQualityInfo !== false;
            
            // أحداث الأزرار
            document.getElementById('close-quality-settings').onclick = () => {
                panel.style.display = 'none';
            };
            
            document.getElementById('save-quality-settings').onclick = () => {
                this.minimumQuality = parseInt(document.getElementById('minimum-quality').value);
                this.autoSelectBest = document.getElementById('auto-select-best').checked;
                this.showQualityInfo = document.getElementById('show-quality-info').checked;
                
                this.saveSettings({
                    minimumQuality: this.minimumQuality,
                    autoSelectBest: this.autoSelectBest,
                    showQualityInfo: this.showQualityInfo
                });
                
                // إشعار بالحفظ
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: #4ecdc4;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 6px;
                    z-index: 1000000;
                    font-size: 14px;
                `;
                notification.textContent = '✅ Quality settings saved!';
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.remove();
                }, 2000);
                
                panel.style.display = 'none';
            };
        },
        
        // إضافة زر إعدادات الجودة
        addQualityButton: function() {
            if (document.getElementById('quality-settings-btn')) {
                return;
            }
            
            const button = document.createElement('button');
            button.id = 'quality-settings-btn';
            button.innerHTML = '⚙️';
            button.title = 'Quality Settings';
            button.style.cssText = `
                position: fixed;
                top: 150px;
                right: 20px;
                width: 50px;
                height: 50px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                border-radius: 50%;
                color: white;
                font-size: 20px;
                cursor: pointer;
                z-index: 999998;
                box-shadow: 0 4px 16px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            `;
            
            button.onmouseover = () => {
                button.style.transform = 'scale(1.1)';
            };
            
            button.onmouseout = () => {
                button.style.transform = 'scale(1)';
            };
            
            button.onclick = () => {
                this.createQualityUI();
                const panel = document.getElementById('quality-settings-panel');
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            };
            
            document.body.appendChild(button);
        }
    };
    
    // تحميل الإعدادات عند بدء التشغيل
    QualitySettings.loadSettings();
    
    // إضافة الزر عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => QualitySettings.addQualityButton(), 2000);
        });
    } else {
        setTimeout(() => QualitySettings.addQualityButton(), 2000);
    }
    
    // تصدير للاستخدام العام
    window.UdemyQualitySettings = QualitySettings;
    
    console.log('🎬 Udemy Quality Settings loaded successfully!');
})();
