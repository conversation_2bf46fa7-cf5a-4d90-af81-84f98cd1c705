// ==UserScript==
// @name         Udemy Advanced Video Downloader
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  أداة متقدمة لتحميل فيديوهات Udemy مع ميزات إضافية
// <AUTHOR>
// @match        https://*.udemy.com/*
// @grant        GM_download
// @grant        GM_setValue
// @grant        GM_getValue
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    // إعدادات التطبيق
    const config = {
        autoDetect: GM_getValue('autoDetect', true),
        downloadQuality: GM_getValue('downloadQuality', 'auto'),
        showNotifications: GM_getValue('showNotifications', true)
    };
    
    // إضافة الأنماط المتقدمة
    const addAdvancedStyles = () => {
        const style = document.createElement('style');
        style.textContent = `
            .udemy-advanced-downloader {
                position: fixed;
                top: 50%;
                right: 20px;
                transform: translateY(-50%);
                width: 320px;
                background: rgba(30, 30, 30, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 16px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.4);
                z-index: 999999;
                font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
                color: white;
                border: 1px solid rgba(255,255,255,0.1);
                overflow: hidden;
            }
            
            .udemy-advanced-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 20px;
                text-align: center;
                position: relative;
            }
            
            .udemy-advanced-title {
                font-size: 18px;
                font-weight: 700;
                margin: 0;
                text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }
            
            .udemy-advanced-subtitle {
                font-size: 12px;
                opacity: 0.8;
                margin: 5px 0 0 0;
            }
            
            .udemy-minimize-btn {
                position: absolute;
                top: 15px;
                right: 15px;
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                width: 28px;
                height: 28px;
                border-radius: 50%;
                cursor: pointer;
                font-size: 16px;
                transition: all 0.3s ease;
            }
            
            .udemy-minimize-btn:hover {
                background: rgba(255,255,255,0.3);
                transform: scale(1.1);
            }
            
            .udemy-advanced-body {
                padding: 20px;
            }
            
            .udemy-section {
                margin-bottom: 20px;
            }
            
            .udemy-section-title {
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 10px;
                color: #4ecdc4;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .udemy-advanced-btn {
                width: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                color: white;
                padding: 14px 18px;
                border-radius: 10px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 8px;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
                position: relative;
                overflow: hidden;
            }
            
            .udemy-advanced-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
            
            .udemy-advanced-btn:active {
                transform: translateY(0);
            }
            
            .udemy-advanced-btn.secondary {
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.2);
            }
            
            .udemy-advanced-btn.secondary:hover {
                background: rgba(255,255,255,0.2);
                box-shadow: 0 8px 25px rgba(255,255,255, 0.1);
            }
            
            .udemy-quality-selector {
                width: 100%;
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.2);
                color: white;
                padding: 10px;
                border-radius: 8px;
                font-size: 14px;
                margin-bottom: 10px;
            }
            
            .udemy-quality-selector option {
                background: #2a2a2a;
                color: white;
            }
            
            .udemy-progress-bar {
                width: 100%;
                height: 6px;
                background: rgba(255,255,255,0.1);
                border-radius: 3px;
                overflow: hidden;
                margin: 10px 0;
            }
            
            .udemy-progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #4ecdc4, #44a08d);
                width: 0%;
                transition: width 0.3s ease;
            }
            
            .udemy-info-card {
                background: rgba(255,255,255,0.05);
                border-radius: 10px;
                padding: 15px;
                font-size: 13px;
                line-height: 1.5;
                border-left: 4px solid #4ecdc4;
            }
            
            .udemy-status-bar {
                background: rgba(0,0,0,0.3);
                padding: 10px 15px;
                font-size: 12px;
                text-align: center;
                border-top: 1px solid rgba(255,255,255,0.1);
            }
            
            .udemy-toggle-advanced {
                position: fixed;
                bottom: 30px;
                right: 30px;
                width: 60px;
                height: 60px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                border-radius: 50%;
                color: white;
                font-size: 24px;
                cursor: pointer;
                z-index: 999998;
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
                transition: all 0.3s ease;
            }
            
            .udemy-toggle-advanced:hover {
                transform: scale(1.1) rotate(10deg);
                box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
            }
            
            .udemy-notification {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(30, 30, 30, 0.95);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.3);
                z-index: 1000000;
                font-size: 14px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.1);
                animation: slideDown 0.3s ease;
            }
            
            @keyframes slideDown {
                from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                to { transform: translateX(-50%) translateY(0); opacity: 1; }
            }
            
            .udemy-checkbox {
                display: flex;
                align-items: center;
                gap: 10px;
                margin: 8px 0;
                font-size: 13px;
            }
            
            .udemy-checkbox input {
                width: 16px;
                height: 16px;
                accent-color: #4ecdc4;
            }
        `;
        document.head.appendChild(style);
    };
    
    // إنشاء واجهة متقدمة
    const createAdvancedUI = () => {
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'udemy-toggle-advanced';
        toggleBtn.innerHTML = '🎬';
        toggleBtn.title = 'Udemy Advanced Downloader';
        
        const panel = document.createElement('div');
        panel.className = 'udemy-advanced-downloader';
        panel.style.display = 'none';
        
        panel.innerHTML = `
            <div class="udemy-advanced-header">
                <h3 class="udemy-advanced-title">🎬 Advanced Downloader</h3>
                <p class="udemy-advanced-subtitle">Professional Video Tools</p>
                <button class="udemy-minimize-btn">−</button>
            </div>
            
            <div class="udemy-advanced-body">
                <div class="udemy-section">
                    <div class="udemy-section-title">📥 Download Options</div>
                    <select class="udemy-quality-selector" id="quality-selector">
                        <option value="auto">Auto Quality</option>
                        <option value="1080">1080p (Full HD)</option>
                        <option value="720">720p (HD)</option>
                        <option value="480">480p (SD)</option>
                    </select>
                    <button class="udemy-advanced-btn" id="download-current">
                        📹 Download Current Video
                    </button>
                    <button class="udemy-advanced-btn secondary" id="batch-download">
                        📦 Batch Download
                    </button>
                </div>
                
                <div class="udemy-section">
                    <div class="udemy-section-title">🔧 Tools</div>
                    <button class="udemy-advanced-btn secondary" id="extract-audio">
                        🎵 Extract Audio
                    </button>
                    <button class="udemy-advanced-btn secondary" id="capture-screenshot">
                        📸 Screenshot
                    </button>
                    <button class="udemy-advanced-btn secondary" id="video-analytics">
                        📊 Video Analytics
                    </button>
                </div>
                
                <div class="udemy-section">
                    <div class="udemy-section-title">⚙️ Settings</div>
                    <label class="udemy-checkbox">
                        <input type="checkbox" id="auto-detect" ${config.autoDetect ? 'checked' : ''}>
                        Auto-detect videos
                    </label>
                    <label class="udemy-checkbox">
                        <input type="checkbox" id="show-notifications" ${config.showNotifications ? 'checked' : ''}>
                        Show notifications
                    </label>
                </div>
                
                <div id="info-display" class="udemy-info-card" style="display: none;"></div>
                <div class="udemy-progress-bar" id="progress-bar" style="display: none;">
                    <div class="udemy-progress-fill" id="progress-fill"></div>
                </div>
            </div>
            
            <div class="udemy-status-bar" id="advanced-status">
                Ready • Click any button to start
            </div>
        `;
        
        document.body.appendChild(toggleBtn);
        document.body.appendChild(panel);
        
        return { toggleBtn, panel };
    };
    
    // وظائف متقدمة
    const advancedFunctions = {
        showNotification: (message, type = 'info') => {
            if (!config.showNotifications) return;
            
            const notification = document.createElement('div');
            notification.className = 'udemy-notification';
            notification.textContent = message;
            
            if (type === 'success') notification.style.borderLeft = '4px solid #4ecdc4';
            if (type === 'error') notification.style.borderLeft = '4px solid #ff6b6b';
            if (type === 'warning') notification.style.borderLeft = '4px solid #ffa726';
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        },
        
        updateProgress: (percent) => {
            const progressBar = document.getElementById('progress-bar');
            const progressFill = document.getElementById('progress-fill');
            
            if (percent > 0) {
                progressBar.style.display = 'block';
                progressFill.style.width = percent + '%';
            } else {
                progressBar.style.display = 'none';
            }
        },
        
        updateStatus: (message) => {
            document.getElementById('advanced-status').textContent = message;
        },
        
        findVideo: () => {
            return document.querySelector('video');
        },
        
        downloadCurrentVideo: () => {
            const video = advancedFunctions.findVideo();
            if (!video) {
                advancedFunctions.showNotification('No video found on this page', 'error');
                return;
            }
            
            const videoSrc = video.src || video.currentSrc;
            if (!videoSrc) {
                advancedFunctions.showNotification('Video source not available', 'error');
                return;
            }
            
            advancedFunctions.updateStatus('Starting download...');
            advancedFunctions.updateProgress(10);
            
            try {
                // استخدام GM_download إذا كان متاحاً
                if (typeof GM_download !== 'undefined') {
                    GM_download(videoSrc, `udemy-video-${Date.now()}.mp4`, videoSrc);
                } else {
                    // Fallback للتحميل العادي
                    const link = document.createElement('a');
                    link.href = videoSrc;
                    link.download = `udemy-video-${Date.now()}.mp4`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
                
                advancedFunctions.updateProgress(100);
                advancedFunctions.showNotification('Download started successfully!', 'success');
                advancedFunctions.updateStatus('Download completed');
                
                setTimeout(() => {
                    advancedFunctions.updateProgress(0);
                    advancedFunctions.updateStatus('Ready');
                }, 2000);
                
            } catch (error) {
                advancedFunctions.showNotification('Download failed: ' + error.message, 'error');
                advancedFunctions.updateStatus('Download failed');
                advancedFunctions.updateProgress(0);
            }
        },
        
        extractAudio: () => {
            const video = advancedFunctions.findVideo();
            if (!video) {
                advancedFunctions.showNotification('No video found', 'error');
                return;
            }
            
            advancedFunctions.showNotification('Audio extraction feature coming soon!', 'warning');
        },
        
        captureScreenshot: () => {
            const video = advancedFunctions.findVideo();
            if (!video) {
                advancedFunctions.showNotification('No video found', 'error');
                return;
            }
            
            try {
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                
                const ctx = canvas.getContext('2d');
                ctx.drawImage(video, 0, 0);
                
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `udemy-screenshot-${Date.now()}.png`;
                    link.click();
                    URL.revokeObjectURL(url);
                    
                    advancedFunctions.showNotification('Screenshot saved!', 'success');
                });
                
            } catch (error) {
                advancedFunctions.showNotification('Screenshot failed: ' + error.message, 'error');
            }
        },
        
        showVideoAnalytics: () => {
            const video = advancedFunctions.findVideo();
            if (!video) {
                advancedFunctions.showNotification('No video found', 'error');
                return;
            }
            
            const infoDisplay = document.getElementById('info-display');
            const formatTime = (seconds) => {
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins}:${secs.toString().padStart(2, '0')}`;
            };
            
            const formatBytes = (bytes) => {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            };
            
            infoDisplay.innerHTML = `
                <strong>📊 Video Analytics</strong><br><br>
                <strong>Basic Info:</strong><br>
                Duration: ${formatTime(video.duration)}<br>
                Current Time: ${formatTime(video.currentTime)}<br>
                Resolution: ${video.videoWidth}x${video.videoHeight}<br>
                Aspect Ratio: ${(video.videoWidth/video.videoHeight).toFixed(2)}:1<br><br>
                
                <strong>Playback:</strong><br>
                Speed: ${video.playbackRate}x<br>
                Volume: ${Math.round(video.volume * 100)}%<br>
                Muted: ${video.muted ? 'Yes' : 'No'}<br><br>
                
                <strong>Technical:</strong><br>
                Ready State: ${video.readyState}/4<br>
                Network State: ${video.networkState}/3<br>
                Buffered: ${video.buffered.length > 0 ? formatTime(video.buffered.end(0)) : 'None'}<br>
                Source: ${video.src ? 'Available' : 'Not found'}
            `;
            
            infoDisplay.style.display = 'block';
            advancedFunctions.showNotification('Analytics loaded', 'success');
        }
    };
    
    // تهيئة التطبيق المتقدم
    const initAdvanced = () => {
        addAdvancedStyles();
        const { toggleBtn, panel } = createAdvancedUI();
        
        // أحداث التبديل
        toggleBtn.addEventListener('click', () => {
            const isVisible = panel.style.display !== 'none';
            panel.style.display = isVisible ? 'none' : 'block';
        });
        
        // إغلاق اللوحة
        panel.querySelector('.udemy-minimize-btn').addEventListener('click', () => {
            panel.style.display = 'none';
        });
        
        // أحداث الأزرار
        document.getElementById('download-current').addEventListener('click', advancedFunctions.downloadCurrentVideo);
        document.getElementById('extract-audio').addEventListener('click', advancedFunctions.extractAudio);
        document.getElementById('capture-screenshot').addEventListener('click', advancedFunctions.captureScreenshot);
        document.getElementById('video-analytics').addEventListener('click', advancedFunctions.showVideoAnalytics);
        
        // أحداث الإعدادات
        document.getElementById('auto-detect').addEventListener('change', (e) => {
            config.autoDetect = e.target.checked;
            GM_setValue('autoDetect', config.autoDetect);
        });
        
        document.getElementById('show-notifications').addEventListener('change', (e) => {
            config.showNotifications = e.target.checked;
            GM_setValue('showNotifications', config.showNotifications);
        });
        
        document.getElementById('quality-selector').addEventListener('change', (e) => {
            config.downloadQuality = e.target.value;
            GM_setValue('downloadQuality', config.downloadQuality);
        });
        
        // رسالة ترحيب
        setTimeout(() => {
            advancedFunctions.showNotification('🎬 Advanced Downloader loaded successfully!', 'success');
        }, 1000);
        
        console.log('🎬 Udemy Advanced Downloader loaded successfully!');
    };
    
    // تشغيل التطبيق
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAdvanced);
    } else {
        initAdvanced();
    }
    
})();
