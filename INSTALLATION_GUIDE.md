# 🎬 دليل تثبيت واستخدام Udemy Downloader

## 📋 المتطلبات

### الطريقة الأولى: Tampermonkey (الأسهل)
1. **تثبيت Tampermonkey:**
   - Chrome: [Tampermonkey Chrome](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
   - Firefox: [Tampermonkey Firefox](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)
   - Edge: [Tampermonkey Edge](https://microsoftedge.microsoft.com/addons/detail/tampermonkey/iikmkjmpaadaobahmlepeloendndfphd)

2. **تثبيت السكريبت:**
   - انسخ محتوى ملف `udemy-simple-downloader.user.js` أو `udemy-advanced-downloader.user.js`
   - افتح Tampermonkey Dashboard
   - اضغط على "Create a new script"
   - الصق الكود واحفظ (Ctrl+S)

### الطريقة الثانية: Greasemonkey (Firefox)
1. تثبيت [Greasemonkey](https://addons.mozilla.org/en-US/firefox/addon/greasemonkey/)
2. نفس خطوات Tampermonkey

## 🚀 كيفية الاستخدام

### النسخة البسيطة (Simple Downloader)

#### الميزات:
- ✅ تحميل الفيديو الحالي
- ✅ عرض معلومات الفيديو
- ✅ نسخ رابط الفيديو
- ✅ واجهة بسيطة وسهلة

#### الاستخدام:
1. اذهب لأي صفحة فيديو في Udemy
2. اضغط على زر ⬇️ في الزاوية اليمنى العلوية
3. اختر الوظيفة المطلوبة:
   - **Download Video**: تحميل الفيديو مباشرة
   - **Video Info**: عرض معلومات مفصلة
   - **Copy Video URL**: نسخ رابط الفيديو

### النسخة المتقدمة (Advanced Downloader)

#### الميزات المتقدمة:
- 🎬 تحميل بجودات مختلفة
- 📦 تحميل متعدد (Batch Download)
- 🎵 استخراج الصوت
- 📸 التقاط لقطة شاشة
- 📊 تحليلات الفيديو المفصلة
- ⚙️ إعدادات قابلة للتخصيص
- 🔔 إشعارات ذكية

#### الاستخدام المتقدم:
1. اضغط على زر 🎬 في الزاوية اليمنى السفلية
2. اختر جودة التحميل من القائمة المنسدلة
3. استخدم الأدوات المختلفة:
   - **Download Current Video**: تحميل الفيديو الحالي
   - **Batch Download**: تحميل متعدد (قريباً)
   - **Extract Audio**: استخراج الصوت (قريباً)
   - **Screenshot**: التقاط صورة من الفيديو
   - **Video Analytics**: تحليلات مفصلة

## ⚙️ الإعدادات

### إعدادات النسخة المتقدمة:
- **Auto-detect videos**: اكتشاف تلقائي للفيديوهات
- **Show notifications**: عرض الإشعارات
- **Download Quality**: جودة التحميل الافتراضية

## 🔧 استكشاف الأخطاء

### المشاكل الشائعة:

#### 1. "No video found"
**الحل:**
- تأكد من وجود فيديو في الصفحة
- انتظر تحميل الفيديو كاملاً
- أعد تحميل الصفحة

#### 2. "Video source not available"
**الحل:**
- الفيديو محمي بـ DRM
- جرب فيديو آخر
- تأكد من تسجيل الدخول في Udemy

#### 3. التحميل لا يعمل
**الحل:**
- تحقق من إعدادات المتصفح للتحميلات
- تأكد من تفعيل Tampermonkey
- جرب النقر بزر الماوس الأيمن واختر "Save as"

#### 4. السكريبت لا يظهر
**الحل:**
- تأكد من تثبيت Tampermonkey
- تحقق من تفعيل السكريبت في Dashboard
- أعد تحميل الصفحة

## 🛡️ الأمان والخصوصية

### ما يفعله السكريبت:
- ✅ يعمل محلياً في متصفحك فقط
- ✅ لا يرسل بيانات لخوادم خارجية
- ✅ لا يحفظ معلومات شخصية
- ✅ مفتوح المصدر ومراجع

### ما لا يفعله:
- ❌ لا يخترق حماية DRM
- ❌ لا يتجاوز قيود Udemy
- ❌ لا يحفظ كلمات المرور
- ❌ لا يتتبع نشاطك

## 📝 ملاحظات مهمة

### القيود:
1. **DRM Protection**: بعض الفيديوهات محمية ولا يمكن تحميلها
2. **Quality Limitations**: الجودة تعتمد على ما يوفره Udemy
3. **Login Required**: تحتاج تسجيل دخول صالح في Udemy
4. **Browser Compatibility**: يعمل مع المتصفحات الحديثة فقط

### نصائح للاستخدام الأمثل:
- 🎯 استخدم النسخة البسيطة للاستخدام العادي
- 🚀 استخدم النسخة المتقدمة للميزات الإضافية
- 💾 تأكد من مساحة كافية للتحميل
- 🔄 أعد تحميل الصفحة إذا واجهت مشاكل

## 🆘 الدعم

### إذا واجهت مشاكل:
1. تحقق من console المتصفح (F12)
2. تأكد من تحديث Tampermonkey
3. جرب إعادة تثبيت السكريبت
4. تحقق من إعدادات المتصفح

### معلومات تقنية:
- **متوافق مع**: Chrome, Firefox, Edge, Safari
- **يتطلب**: Tampermonkey أو Greasemonkey
- **حجم الكود**: أقل من 50KB
- **الأداء**: لا يؤثر على سرعة التصفح

## 🔄 التحديثات

السكريبت يتحدث تلقائياً عبر Tampermonkey. للتحديث اليدوي:
1. افتح Tampermonkey Dashboard
2. اذهب لتبويب "Installed userscripts"
3. اضغط على "Check for userscript updates"

## ⚖️ إخلاء المسؤولية

هذا السكريبت مخصص للاستخدام التعليمي والشخصي فقط. يرجى احترام حقوق الطبع والنشر وشروط استخدام Udemy. المطور غير مسؤول عن أي استخدام غير قانوني.
